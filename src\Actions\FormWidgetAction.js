import * as ActionTypes from "../Constants/FormWidgetConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const getForms = queue => dispatch => {
    dispatch(formWidgetLoading())
    apiClient.get(`/api/agent/form/${queue}`).then(r => dispatch(formWidgetSuccess(r.data))).catch(e => dispatch(formWidgetFailed(handleError(e))))
}

const formWidgetLoading = () => ({
    type: ActionTypes.FORM_LOADING
})

const formWidgetSuccess = message => ({
    type: ActionTypes.FORM_SUCCESS,
    payload: message
})

const formWidgetFailed = err => ({
    type: ActionTypes.FORM_FAILED,
    payload: err
})