import * as ActionTypes from "../Constants/QueueConstant";

const initialState = {
    data: [],
    errMess: false,
    isLoading: false,
    category: [],
    sms: [],
    message: null
}

export const SMSReducer = (state = initialState, action) => {

    switch (action.type) {
        default:
            return state
        case "SMS_LOADING":
            // return { ...state, isLoading: true, message: null }
            return { ...state, isLoading: false, message: null }
        case "SMS_CATEGORY":
            return { ...state, category: action.payload, isLoading: false, message: null }
        case "SMS":
            return { ...state, sms: action.payload, isLoading: false, message: null }
        case "SMS_SUCCESS":
            return { ...state, message: action.payload, isLoading: false }
        case "SMS_FAILED":
            return { ...state, errMess: action.payload, isLoading: false, message: null }
    }
}