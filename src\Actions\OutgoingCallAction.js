import * as ActionTypes from "../Constants/OutgoingCallConstant"
import apiClient from "../config/apiClient";
import { handleError } from "../Shared/handleError";

export const getOutgoingCallId = () => dispatch => {
    dispatch(callLoading())
    dispatch(resetCallId())
    apiClient.post(`/api/agent/outgoing-channel`).then(r => dispatch(callSuccess(r.data))).catch(e => dispatch(callFailed(handleError(e))))
}
const resetCallId = () => ({
    type: "RESET_CALLID"
})

const callLoading = () => ({
    type: ActionTypes.OUTGOING_ID_LOADING
})

const callSuccess = callId => ({
    type: ActionTypes.OUTGOING_ID_SUCCESS,
    payload: callId
})

const callFailed = err => ({
    type: ActionTypes.OUTGOING_ID_FAILED,
    payload: err
})