import * as ActionTypes from "../Constants/CallReminderConstant"

const initialState = {
    message: false,
    errMess: false,
    isLoading: false
}

export const CallReminderReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.CALLREMINDER_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.CALLREMINDER_SUCCESS:
            return { ...state, message: action.payload, errMess: false, isLoading: false }
        case ActionTypes.CALLREMINDER_FAILED:
            return { ...state, errMess: action.payload, message: false, isLoading: false }
        case ActionTypes.RESET_CALLREMINDER_MESSAGE:
            return { ...state, message: false, errMess: false }
    }

}