import * as ActionTypes from "../Constants/CallReportFormConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const postCallReportForm = data => dispatch => {
    dispatch(callReportFormLoading())
    apiClient.post(`/api/remark`, data).then(r => dispatch(callReportFormSuccess(r.data))).catch(e => dispatch(callReportFormFailed(handleError(e))))
}

const callReportFormLoading = () => ({
    type: ActionTypes.CALLREPORTFORM_LOADING
})

const callReportFormSuccess = message => ({
    type: ActionTypes.CALLREPORTFORM_SUCCESS,
    payload: message
})

const callReportFormFailed = err => ({
    type: ActionTypes.CALLREPORTFORM_FAILED,
    payload: err
})