import * as ActionTypes from "../Constants/CallReportFormConstant"

const initialState = {
    message: false,
    errMess: false,
    isLoading: false
}

export const CallReportFormReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.CALLREPORTFORM_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.CALLREPORTFORM_SUCCESS:
            return { ...state, message: action.payload, errMess: false, isLoading: false }
        case ActionTypes.CALLREPORTFORM_FAILED:
            return { ...state, errMess: action.payload, message: false, isLoading: false }
        case ActionTypes.RESET_CALLREPORTFORM_MESSAGE:
            return { ...state, message: false, errMess: false }
    }

}