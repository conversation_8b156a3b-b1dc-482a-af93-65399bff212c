import * as ActionTypes from "../Constants/QueueConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";
import initializeEcho from '../config/echo';
import Pusher from 'pusher-js';

export const getQueues = (userId) => dispatch => {
    const echo = initializeEcho(sessionStorage.getItem('agent_token'));

    dispatch(queueLoading())
    // apiClient.get(`/api/agent/get-queue`).then(r => dispatch(queueSuccess(r.data))).catch(e => dispatch(queueFailed(handleError(e))))

    const channel = echo.private(`agent-panel-queue-channel.${userId}`);
    channel.listen(".agent-panel-queue", (e) => {
        // console.log("Sockect Get-Queue Data : " + JSON.stringify(e));
        dispatch(queueSuccess(e.data.data))
    });

    return () => {
        channel.stopListening(".agent-panel-queue");
    };
}

const queueLoading = () => ({
    type: ActionTypes.QUEUE_LOADING
})

const queueSuccess = queues => ({
    type: ActionTypes.QUEUE_SUCCESS,
    payload: queues
})

const queueFailed = err => ({
    type: ActionTypes.QUEUE_FAILED,
    payload: err
})