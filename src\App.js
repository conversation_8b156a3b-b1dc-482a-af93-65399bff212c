import "./App.less";
import Main from "./pages/Main";
import { Browser<PERSON>outer as Router } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import { Provider, useSelector } from "react-redux";
import ChatIcon from "./components/ChatIcon";
import { ConfigureStore } from "./App/store";
import { useState, useEffect } from "react";
import { dispatchBroadcastEvents, fetchDomain, fetchUser } from "./config/queries";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";



const queryClient = new QueryClient();
const store = ConfigureStore();

function App() {
    const [isLoggedIn,setIsLoggedIn] = useState(sessionStorage.getItem('loggedin') ? true : false)

    useEffect(() => {
        dispatchBroadcastEvents();
    }, []);

    return (
        <QueryClientProvider client={queryClient}>
            <Provider store={store}>
                <Router basename="/user">
                    {/* <ChatIcon /> */}
                    <AuthWrapper isLoggedIn={isLoggedIn} />
                    <Main setIsLoggedIn={setIsLoggedIn}/>
                </Router>
            </Provider>
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}

function AuthWrapper({isLoggedIn}) {

    // useEffect(()=>{
    //     console.log('dddddddddd',isLoggedIn)
    // },[isLoggedIn])

    return isLoggedIn ?<ChatIcon /> : <></> 
}




export default App;
