const initialState = {
    data: [],
    errMess: false,
    isLoading: false
}

export const ScriptReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "SCRIPT_LOADING":
            return {...state, isLoading: true}
        case "SCRIPT_SUCCESS":
            return {...state, data: action.payload, isLoading: false}
        case "SCRIPT_FAILED":
            return {...state, errMess: action.payload, isLoading: false}
    }
}