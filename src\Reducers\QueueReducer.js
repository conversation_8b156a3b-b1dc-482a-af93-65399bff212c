import * as ActionTypes from "../Constants/QueueConstant"
const initialState = {
    queues: [],
    errMess: false,
    isLoading: false
}

export const QueueReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.QUEUE_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.QUEUE_SUCCESS:
            return { ...state, queues: action.payload, isLoading: false }
        case ActionTypes.QUEUE_FAILED:
            return { ...state, errMess: action.payload, isLoading: false }
    }
}