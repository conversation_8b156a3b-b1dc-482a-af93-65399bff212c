import * as ActionTypes from "../Constants/FormDataConstant"
import apiClient from "../config/apiClient";
import { handleError } from "../Shared/handleError";

export const postFormData = data => dispatch => {
    dispatch(formDataLoading())
    apiClient.post(`/api/formData`, data).then(r => dispatch(formDataSuccess(r.data))).catch(e => dispatch(formDataFailed(handleError(e))))
}

const formDataLoading = () => ({
    type: ActionTypes.FORM_DATA_LOADING
})

const formDataFailed = err => ({
    type: ActionTypes.FORM_DATA_FAILED,
    payload: err
})

const formDataSuccess = m => ({
    type: ActionTypes.FORM_DATA_SUCCESS,
    payload: m
})