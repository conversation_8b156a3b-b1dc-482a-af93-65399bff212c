import React, { useEffect, useRef } from "react";
import Timer from "react-compound-timer/build";

function TimerComponent({ time, active, title }) {
  const intervalRef = useRef(null);
  const timeRef = useRef(null);

  useEffect(() => {
    if (time) {
      timeRef.current.setTime(time * 1000);
    }
  }, [time]);

  useEffect(() => {
    if (active) {
      timeRef.current.start();
    } else {
      timeRef.current.pause();
    }
  }, [active]);

//   const formatTime = (time) => {
//     const hours = Math.floor(time / 3600)
//       .toString()
//       .padStart(2, "0");
//     const minutes = Math.floor((time % 3600) / 60)
//       .toString()
//       .padStart(2, "0");
//     const seconds = Math.floor(time % 60)
//       .toString()
//       .padStart(2, "0");
//     return `${hours}:${minutes}:${seconds}`;
//   };

  return (
    <>
      <Timer
        ref={timeRef}
        initialTime={time ? time * 1000 : 0}
        formatValue={(value) => `${value < 10 ? `0${value}` : value}`}
        startImmediately={false}
        onStop={() => clearInterval(intervalRef.current)}
      >
        {({ start, resume, pause, stop, reset, timerState }) => (
          <>
            {title} <Timer.Hours />:<Timer.Minutes />:<Timer.Seconds />
          </>
        )}
      </Timer>
    </>
  );
}

export default TimerComponent;