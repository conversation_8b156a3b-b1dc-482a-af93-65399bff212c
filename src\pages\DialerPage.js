import React, { useEffect, useState } from "react"
import { useQuery } from "react-query";
import { fetchDomain, fetchQueue, fetchUser, fetchPrepaidBil } from "../config/queries";
import { Button, Result, Skeleton } from "antd";
import { LogoutOutlined } from "@ant-design/icons";
import DialerLayout from "../dialer/DialerLayout";
import { useMemo } from "react";
import initializeEcho from '../config/echo';
import Pusher from 'pusher-js';
import axios from "axios";

const DialerPage = ({ handleLogout,setIsLoggedIn }) => {

    const userId = sessionStorage.getItem('id');
    const echo = initializeEcho(sessionStorage.getItem('agent_token'));

    const options = {
        refetchInterval: false,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
        retry: false
    }
    
    // const domainQuery = useQuery('fetchDomain', fetchDomain, options)
    const [domainQuery, setDomainQuery] = useState({
            status: "idle",
            isLoading: false,
            isSuccess: false,
            isError: false,
            isIdle: true,
            data: null,
            dataUpdatedAt: 0,
            error: null,
            errorUpdatedAt: 0,
            failureCount: 0,
            errorUpdateCount: 0,
            isFetched: false,
            isFetchedAfterMount: false,
            isFetching: false,
            isRefetching: false,
            isLoadingError: false,
            isPlaceholderData: false,
            isPreviousData: false,
            isRefetchError: false,
            isStale: false,
        });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-systemsetting-channel.${userId}`);

        setDomainQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));
        
        channel.listen(".agent-panel-systemsetting", (e) => {
            setDomainQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data[0],
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-systemsetting");
        };
    }, [userId]);

    // const userQuery = useQuery('fetchUser', fetchUser, options)
    const [userQuery, setUserQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-getuser-channel.${userId}`);

        setUserQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));
        
        channel.listen(".agent-panel-getuser", (e) => {
            // console.log("agent-panel-getuser Final Data : " + JSON.stringify(e));
            setUserQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-getuser");
        };
    }, [userId]);

    // const queueQuery = useQuery('fetchQueue', fetchQueue, options)
    const [queueQuery, setQueueQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-queue-channel.${userId}`);

        setQueueQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));
        
        channel.listen(".agent-panel-queue", (e) => {
            // console.log("Sockect Get-Queue Data : " + JSON.stringify(e));
            setQueueQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-queue");
        };
    }, [userId]);

    // useEffect(() => {
    //     if (queueQuery !== null) {
    //         console.log("Sockect Get-Queue Final:", queueQuery);
    //     }
    // }, [queueQuery]);


    const [data, setData] = useState({
        user: {

        },
        domain: {},
        queue: {}
    })
    const [loading, setLoading] = useState(true)
    const [errorFlag, setErrorFlag] = useState(false)

    useEffect(() => {
        fetchAllData()
            .then(r => setData(r))
            .catch(e => setErrorFlag(true))
            .finally(() => setLoading(false))
    }, [])


    const fetchAllData = async () => ({
        'domain': await fetchDomain(),
        'user': await fetchUser(),
        'queue': await fetchQueue(),
        'prepaid' : await fetchPrepaidBil()
    })

    // useMemo(() => {
    //     sessionStorage.setItem('auto_call_answer',data.domain?.auto_call_answer)
    //     sessionStorage.setItem('auth_username', data.user.auth_username)
    // }, [data.user.auth_username,data.domain?.auto_call_answer])

    useEffect(() => {
        sessionStorage.setItem('auto_call_answer',domainQuery?.data?.auto_call_answer)
        sessionStorage.setItem('enable_call_hangup',domainQuery?.data?.enable_call_hangup)
        sessionStorage.setItem('auth_username', userQuery?.data?.auth_username ?? null)
        sessionStorage.setItem('view_callback', userQuery?.data?.view_callback)
    }, [userQuery?.data, domainQuery?.data]);
    
    return (
        <Skeleton loading={loading}>
            {data && data.user.type === 'Normal' ? <>
                <Result
                    status="error"
                    title="401"
                    subTitle="Sorry, you are unauthorized to login into this application. Please contact your system administrator."
                    extra={<Button onClick={handleLogout} icon={<LogoutOutlined />} type="primary">Logout</Button>}
                />
            </> : errorFlag ? <>
                <Result
                    status={500}
                    title={500}
                    subTitle={"Error in fetching parameters from server. Please contact your system administrator."}
                    extra={<Button onClick={() => window.location.reload()} type="primary">Refresh Page</Button>}
                />
            </> : <>
                <DialerLayout
                    user={data.user}
                    name={data.user.name}
                    sipDomain={data.domain.server_address}
                    authUser={data.user.auth_username}
                    authPass={data.user.auth_password}
                    wssPort={data.domain.wss_port}
                    queues={data.queue}
                    settings={data.domain}
                    setIsLoggedIn={setIsLoggedIn}
                />
            </>}
        </Skeleton>
    )

    /*if(userQuery.isSuccess && userQuery.data.type === "Normal") {
        return (
            <Result
                status="error"
                title="401"
                subTitle="Sorry, you are unauthorized to login into this application. Please contact your system administrator."
                extra={<Button icon={<LogoutOutlined />} type="primary">Logout</Button>}
            />
        )
    } else if (userQuery.isError || domainQuery.isError) {
        return (
          <Result
            status={userQuery.error.status ?? 500}
            title={userQuery.error.status ?? 500}
            subTitle={userQuery.error.statusText ?? error}
            extra={<Button onClick={() => window.location.reload()} type="primary">Refresh Page</Button>}
          />
        )
    } else if(domainQuery.isLoading || userQuery.isLoading) {
        return (
            <Row justify="center">
                <Col span={20}>
                    <Skeleton paragraph={{ rows: 10 }} active />
                </Col>
            </Row>
        )
    } else return (
        <Spin spinning={domainQuery.isLoading || userQuery.isLoading}>
            <DialerLayout
                name={userQuery.data.name}
                sipDomain={domainQuery.data.server_address}
                authUser={userQuery.data.auth_username}
                authPass={userQuery.data.auth_password}
                wssPort={domainQuery.data.wss_port}
                queues={queueQuery.data}
                settings={domainQuery.data}
            />
        </Spin>
    )*/
}

export default DialerPage