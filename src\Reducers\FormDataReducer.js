import * as ActionTypes from "../Constants/FormDataConstant"

const initialState = {
    isLoading: false,
    message: false,
    errMess: false
}

export const FormDataReducer = (state = initialState, action) => {
    switch (action.type) {
        case ActionTypes.FORM_DATA_LOADING:
            return {...state, isLoading: true, errMess: false, message: false}
        case ActionTypes.FORM_DATA_SUCCESS:
            return {...state, message: action.payload, isLoading: false, errMess: false}
        case ActionTypes.FORM_DATA_FAILED:
            return {...state, errMess: action.payload, isLoading: false, message: false}
        default:
            return state
    }
}