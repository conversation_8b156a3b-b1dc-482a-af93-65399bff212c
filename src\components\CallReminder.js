import { DatePicker, Form, Input, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { postCallReminder } from "../Actions/CallReminderAction";
import { SaveOutlined } from "@ant-design/icons";
import openNotificationWithIcon from "./Notification";
import openSuccessNotificationWithIcon from "./Message";
import { replayNotification } from "../Shared/notification";
import apiClient from "../config/apiClient";

export const CallReminder = ({ callReminder, setCallReminder,setCallReportForm }) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const callReminderState = useSelector((state) => state.CallReminderReducer);
    const { callId } = useSelector((state) => state.CallReducer);
    const { outgoingCallId } = useSelector((state) => state.OutgoingCallReducer);
    const userId = sessionStorage.getItem('id');
    useEffect(() => {
        if (!callReminderState.isLoading && callReminderState.errMess) {
            openNotificationWithIcon("error", callReminderState.errMess);
            dispatch({ type: "RESET_CALLREMINDER_MESSAGE" });
        } else if (!callReminderState.isLoading && callReminderState.message) {
            openSuccessNotificationWithIcon(callReminderState.message);
            dispatch({ type: "RESET_CALLREMINDER_MESSAGE" });
        }
    }, [callReminderState, dispatch]);

    useEffect(() => {
        if (form && callReminder) {
            form.setFieldsValue({
                channel: callId || outgoingCallId,
            });
        }
    }, [callId, outgoingCallId, callReminder, form]);

    return (
        <Modal
            visible={callReminder}
            onCancel={() =>{
                 setCallReminder(false)
                 setCallReportForm(true)
            }}
            okText="Submit"
            title="Schedule Call"
            closable={false} 
            // cancelButtonProps={{ style: { display: 'none' } }}
            onOk={() =>
                form
                    .validateFields()
                    .then((values) => { 
                        const payload = { ...values, date: values.date.format("YYYY-MM-DD HH:mm:ss"), created_by: parseInt(userId, 10) };
                        //  dispatch(postCallReminder(payload)) 
                         apiClient.post('/api/callback',payload).then((res)=>{
                            openNotificationWithIcon('success',res.data?.message)
                            setCallReportForm(true)
                         }).catch(()=>{
                            openNotificationWithIcon('error',"Something went wrong!")
                            setCallReportForm(true)
                         })
                    })
                    .then(() => form.resetFields(["queue", "code"]))
                    .then(() => setCallReminder(false))
                    .catch((e) => console.log(e))
            }
            okButtonProps={{
                loading: callReminderState.isLoading,
                icon: <SaveOutlined />,
            }}
        >
            <Form layout={"vertical"} form={form}>
                <Form.Item
                    label="Unique ID"
                    name="uniqueid"
                >
                    <Input placeholder="Unique Id"/>
                </Form.Item>
                <Form.Item
                    label="Customer Number"
                    name="number"
                    rules={[
                        {
                            required: true,
                            message: 'Customer number is required!',
                        }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    label="Date & Time"
                    name="date"
                    rules={[
                        {
                            required: true,
                            message: 'Duration is required!',
                        }
                    ]}
                >
                    <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" style={{ width: "100%" }} />
                </Form.Item>
            </Form>
        </Modal>
    );
};