import * as ActionTypes from "../Constants/CallReminderConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const postCallReminder = data => dispatch => {
    dispatch(callReminderLoading())
    apiClient.post(`/api/callback`, data).then(r => dispatch(callReminderSuccess(r.data.message))).catch(e => dispatch(callReminderFailed(handleError(e))))
}

const callReminderLoading = () => ({
    type: ActionTypes.CALLREMINDER_LOADING
})

const callReminderSuccess = message => ({
    type: ActionTypes.CALLREMINDER_SUCCESS,
    payload: message
})

const callReminderFailed = err => ({
    type: ActionTypes.CALLREMINDER_FAILED,
    payload: err
})