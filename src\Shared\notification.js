import { notification } from "antd";

const store = new Map();

function makeKey(prefix = "notif") {
    if (typeof crypto !== "undefined" && crypto.randomUUID) {
        return `${prefix}-${crypto.randomUUID()}`;
    }
    return `${prefix}-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
}

export function saveNotification(props = {}) {
    const key = String(props.key || makeKey("notif"));
    const finalProps = { ...props, key };
    store.set(key, finalProps);
    notification.open(finalProps);
    return key;
}

export function replayNotifications() {
    for (const [key, props] of store.entries()) {
        notification.open({ ...props, key });
    }
}

export function replayNotification(key) {
    const props = store.get(String(key));
    if (props) notification.open({ ...props, key: String(key) });
}

export function removeNotification(key) {
    store.delete(String(key));
}

export function closeAllNow() {
    notification.destroy();
}

export function clearNotifications() {
    store.clear();
}

export function listNotificationKeys() {
    return Array.from(store.keys());
}
