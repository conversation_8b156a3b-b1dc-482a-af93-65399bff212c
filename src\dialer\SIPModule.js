import React, {
    Component,
    createRef,
    useState,
    useEffect,
    useRef,
    useContext,
} from "react";
import {
    AudioMutedOutlined,
    CloseCircleFilled,
    CloseOutlined,
    ClusterOutlined,
    CodeOutlined,
    CodeSandboxOutlined,
    ContactsOutlined,
    DeliveredProcedureOutlined,
    FieldTimeOutlined,
    CustomerServiceTwoTone,
    GatewayOutlined,
    NotificationOutlined,
    PauseCircleOutlined,
    PhoneFilled,
    PhoneOutlined,
    PhoneTwoTone,
    StopOutlined,
    SwapLeftOutlined,
    ClockCircleOutlined,
    SwapOutlined,
    UserOutlined,
    LoadingOutlined,
} from "@ant-design/icons";
import {
    Button,
    Card,
    Col,
    Descriptions,
    Divider,
    Input,
    Modal,
    Row,
    Space,
    Spin,
    Statistic,
    Tag,
    Typography,
} from "antd";
import {
    Invitation,
    Inviter,
    Registerer,
    RegistererState,
    SessionState,
    TransportState,
    UserAgent,
    Web,
} from "sip.js";
import openNotificationWithIcon from "../components/Notification";
import DialerMenu from "./DialerMenu";
import Notifier from "react-desktop-notification";
import DialerAccount from "./DialerAccount";
//import Timer from "simple-react-timer"
import Timer from "react-compound-timer"
import openSuccessNotificationWithIcon from "../components/Message"
import ring from "../ring.mp3"
// import {AppContext} from "../APIContext";
import apiClient from "../config/apiClient";
import TimerComponent from "../components/TimerComponent";
import { useQuery } from "react-query";
import { fetchDomain } from "../config/queries";
import initializeEcho from '../config/echo';
import { ScheduleCallBackTable } from "../components/ScheduleCallBackTable";

const IncomingModal = ({
    isModalVisible,
    isCallAccepted,
    setIsCallAccepted,
    setFormWidgetDataOnCallAcceptance,
    formWidgetDataOnCallAcceptance,
    onOk,
    onCancel,
    number,
    settings,
    setIncomingCallAccepted,
    dtmfInput
}) => {
    const autoCallAnswer = sessionStorage.getItem('auto_call_answer') ?? 0;
    useEffect(() => {
        // if (isModalVisible && settings.auto_call_answer == 1) {
        //     onOk();
        // }
        if (isModalVisible) {
            gotNewNotification();
        }
    }, [isModalVisible]);

    // useEffect(() => {
    //     if (isCallAccepted) {
    //         apiClient
    //             .get(`api/getFormData?phone_number=${number}`)
    //             .then((res) => {
    //                 setFormWidgetDataOnCallAcceptance(res.data.data);
    //             })
    //             .catch((err) => console.log("Error: ", err));
    //     }
    // }, [isCallAccepted]);

    const gotNewNotification = () => {
        Notifier.focus("Incoming Call", `From: ${number}`);
    };

    const modalHeader = (
        <>
            Incoming call from: {number}
            <br />
            {dtmfInput ? dtmfInput : ''}
        </>
    );

    const okProps = {
        icon: <PhoneFilled />,
    };

    const cancelProps = {
        icon: <CloseCircleFilled />,
    };

    return (
        <>
            <Modal
                title="Incoming call"
                visible={isModalVisible}
                onOk={onOk}
                onCancel={onCancel}
                maskClosable={false}
                centered={true}
                keyboard={false}
                okText="Accept"
                cancelText="Reject"
                destroyOnClose={true}
                okButtonProps={okProps}
                cancelButtonProps={cancelProps}
            >
                <p>{modalHeader}</p>
            </Modal>
        </>
    );
};

export const TransferModal = (props) => {
    const [transferNumber, setTransferNumber] = useState();

    const initiateTransfer = () => {
        if (transferNumber.length === 0) return;
        if (props.transferType === "blind")
            props.toggleBlindTransfer(transferNumber);
        else props.toggleAttendedTransfer(transferNumber);
    };

    const handleHold = () => {
        if (props.isHold) {
            props.onUnhold();
        } else {
            props.onHold();
        }
    };

    const handleMute = () => {
        if (props.isMute) {
            props.onUnmute();
        } else {
            props.onMute();
        }
    };

    return (
        <Modal
            title={props.transferType === "blind" ? "Transfer Call" : "Conference"}
            centered
            okText="Dial"
            onOk={initiateTransfer}
            visible={props.visible}
            onCancel={props.onCancel}
        >
            <Input
                type="text"
                value={transferNumber}
                onChange={(e) => setTransferNumber(e.target.value)}
            />
            <div style={{ textAlign: "center", marginTop: 10 }}>
                <Tag>
                    <Timer startTime={Date.now()} />
                </Tag>
                <Tag
                    icon={<PhoneOutlined />}
                    color={props.isConnected ? "green" : "#f50"}
                >
                    {props.isConnected ? "connected" : "disconnected"}
                </Tag>
                <Tag
                    icon={<GatewayOutlined />}
                    color={props.isBridged ? "green" : "#f50"}
                >
                    {props.isBridged ? "bridged" : "unbridged"}
                </Tag>
                <Button
                    onClick={handleHold}
                    disabled={!props.isConnected}
                    danger={props.isHold}
                >
                    <PauseCircleOutlined />
                </Button>
                <Button
                    onClick={handleMute}
                    disabled={!props.isConnected}
                    danger={props.isMute}
                >
                    <AudioMutedOutlined />
                </Button>
            </div>
            <div style={{ textAlign: "center", marginTop: 10 }}>
                <Button onClick={props.onAcceptTransfer} disabled={!props.isConnected}>
                    <PhoneOutlined /> Bridge Call(s)
                </Button>
                <Button
                    onClick={() => {
                        props.onTransferHangup();
                        props.setVisible(false);
                    }}
                    disabled={!props.isConnected}
                    type="primary"
                >
                    <StopOutlined /> Hangup
                </Button>
            </div>
        </Modal>
    );
};

const IncomingCall = (props) => {
    const [timer, setTimer] = useState("00:00");
    const [transferType, setTransferType] = useState("");
    const [visible, setVisible] = useState(false);
    const [callId, setCallId] = useState(null);
    const [onHold, setOnHold] = useState(false);
    const [ratingSettingsForIncoming, setRatingSettingsForIncoming] = useState({
        status: 0
    })

    const timerRef = useRef();

    const userId = sessionStorage.getItem('id');
    const echo = initializeEcho(sessionStorage.getItem('agent_token'));

    useEffect(() => {
        switch (visible) {
            case true:
                if (props.isConnected && !props.isBridged) props.holdCall();
                break;
            case false:
                if (props.isConnected && !props.isBridged) props.unholdCall();
                break;
            default:
                break;
        }
    }, [visible]);

    const title = (
        <>
            <PhoneOutlined /> {props.number}
            {props?.dtmfInput && <>(<span title={props?.dtmfInput}>({props?.dtmfInput})</span>)</>}
        </>
    );

    const toggleHold = () => {
        if (props.isHold) {
            setOnHold(true);
            props.unholdCall().then(() => {
                apiClient
                    .post("api/agent/un-hold", {
                        queue: props.queue[0],
                        callId: props.callId,
                    })
                    .then((r) => console.log(r.data))
                    .catch((e) => console.log(e))
                    .finally(() => setOnHold(false));
            });
        } else {
            setOnHold(true);
            props.holdCall().then(() => {
                apiClient
                    .post("api/agent/hold", {
                        queue: props.queue[0],
                        callId: props.callId,
                    })
                    .then((r) => console.log(r.data))
                    .catch((e) => console.log(e))
                    .finally(() => setOnHold(false));
            });
        }
    };

    const toggleMute = () => {
        if (props.isMute) props.unmuteCall();
        else props.muteCall();
    };

    const toggleTransfer = (type) => {
        if (type === "blind") setTransferType("blind");
        else setTransferType("attended");
        setVisible(true);
    };

    useEffect(() => {
        const current = timerRef.current;
        if (props.isConnected && props.incoming) {
            current.start();
        }
        return () => current?.reset();
    }, [props.isConnected]);

    const toggleBlindTransfer = target => {
        props.blindTransfer(target)
    }

    const toggleAttendedTransfer = target => {
        props.attendedTransfer(target)
    }

    const onCancel = () => {
        setVisible(false)
        setTransferType('')
    }

    useEffect(() => {
        const current = timerRef.current
        if (props.isConnected && props.incoming) {
            current.start()
        }
        return () => current?.reset()
    }, [props.isConnected])

    const getServiceRatingSettings = () => apiClient.get('/api/getServiceRatingSettings')
    const dispatchServiceRating = values => apiClient.post('/api/serviceRating', values)

    const handleServiceRatingClick = () => {
        dispatchServiceRating({ extension: props.number, agentId: props.authUser })
            .then(r => openSuccessNotificationWithIcon(r.data))
            .catch(e => console.log(e))
    }

    useEffect(() => {
        getServiceRatingSettings()
            .then(r => setRatingSettingsForIncoming(r.data))
            .catch(e => console.log(e))
    }, [])

    // const domainQuery = useQuery('fetchDomain', fetchDomain)
    const [domainQuery, setDomainQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-systemsetting-channel.${userId}`);

        setDomainQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-systemsetting", (e) => {
            setDomainQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data[0],
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-systemsetting");
        };
    }, [userId]);


    // console.log("domain data new 03 : ", domainQuery?.data)

    // console.log("In domain ", domainQuery?.data?.enable_call_hangup)

    if ((props.incoming && props.isConnected) || props.isTransferConnected) {
        return (
            <Row style={{ marginTop: 10, marginBottom: 10 }}>
                <Col>
                    <Card
                        style={{ textAlign: 'center' }}
                        size="small"
                        title={title}
                        actions={[

                            <PhoneTwoTone onClick={domainQuery?.data?.enable_call_hangup == 1 ? props.endCall : () => console.log("here")} title="Hangup" twoToneColor="red" />,
                            <AudioMutedOutlined style={{ color: props.isMute ? 'lightcoral' : '' }} onClick={toggleMute} title="Mute" />,
                            <NotificationOutlined style={{ color: props.isHold ? 'lightcoral' : '' }} onClick={toggleHold} title="Hold" />,
                            <SwapOutlined onClick={() => toggleTransfer('blind')} title="Transfer" />,
                            <ContactsOutlined onClick={() => toggleTransfer('attended')} title="Conf" />,
                            // <CustomerServiceTwoTone onClick={ratingSettingsForIncoming.status === 0 ? null : handleServiceRatingClick} title="Service Rating" />
                            <CustomerServiceTwoTone onClick={() => {
                                if (ratingSettingsForIncoming.status !== 0) {
                                    handleServiceRatingClick();
                                }
                            }}
                                title="Service Rating"
                            />

                        ]}
                        extra={<CloseOutlined onClick={props.endCall} style={{ fontSize: 10 }} />}
                    >
                        <Tag color={props.isConnected ? "success" : "error"}>{props.isConnected ? "connected" : "disconnected"}</Tag>
                        {/*<Tag>{timer}</Tag>*/}
                        <Tag>
                            {/*<Timer startTime={!!props.isConnected} />*/}
                            <Timer
                                ref={timerRef}
                                startImmediately={false}
                                formatValue={value => `${(value < 10 ? `0${value}` : value)}`}
                            >
                                {({ start, resume, pause, stop, reset, timerState }) => (
                                    <>
                                        <div>
                                            <Timer.Hours />:<Timer.Minutes />:<Timer.Seconds />
                                        </div>
                                    </>
                                )}
                            </Timer>
                        </Tag>
                        {props.isHold ? <Tag icon={<NotificationOutlined />} color="#cd201f">
                            Hold
                        </Tag> : ''}
                        {props.isMute ? <Tag icon={<AudioMutedOutlined />} color="#cd201f">
                            Mute
                        </Tag> : ''}
                    </Card>
                    <TransferModal
                        transferType={transferType}
                        toggleBlindTransfer={toggleBlindTransfer}
                        toggleAttendedTransfer={toggleAttendedTransfer}
                        visible={visible}
                        onCancel={onCancel}
                        onAcceptTransfer={props.onAcceptTransfer}
                        isConnected={props.isTransferConnected}
                        isHold={props.isTransferHold}
                        isMute={props.isTransferMute}
                        onHold={props.onTransferHold}
                        onUnhold={props.onTransferUnhold}
                        onMute={props.onTransferMute}
                        onUnmute={props.onTransferUnmute}
                        onTransferHangup={props.onTransferHangup}
                        isBridged={props.isBridged}
                        setVisible={setVisible}
                    />
                </Col>
            </Row>
        )
    } else return ''
}

export default class SIPModule extends Component {
    constructor(props) {
        super(props);

        this.state = {
            sipDomain: this.props.sipDomain,
            name: this.props.name,
            authUser: this.props.authUser,
            authPass: this.props.authPass,
            wssPort: this.props.wssPort,
            queues: this.props.queues,
            uri: UserAgent.makeURI(
                `sip:${this.props.authUser}@${this.props.sipDomain}`
            ),
            _transferredSession: null,
            _session: null,
            _connected: false,
            _registered: false,
            userAgent: null,
            registerer: null,
            isModalVisible: false,
            dialedNumber: "",
            incoming: false,
            outgoing: false,
            isConnected: false,
            isHold: false,
            isMute: false,
            sessionState: "",
            isTransferHold: false,
            isTransferMute: false,
            isTransferConnected: false,
            isBridged: false,
            dtmfInput: null
        };

        this.onEndCall = this.onEndCall.bind(this);
        this.onConnect = this.onConnect.bind(this);
        this.onDisconnect = this.onDisconnect.bind(this);
        this.registerListener = this.registerListener.bind(this);
        this.onInvite = this.onInvite.bind(this);
        this.registerEvents = this.registerEvents.bind(this);
        this.registerIncomingEvents = this.registerIncomingEvents.bind(this);
        this.onCallAccept = this.onCallAccept.bind(this);
        this.attachMedia = this.attachMedia.bind(this);
        this.cleanupMedia = this.cleanupMedia.bind(this);
        this.sessionListener = this.sessionListener.bind(this);
        this.incomingSessionListener = this.incomingSessionListener.bind(this);
        this.setError = this.setError.bind(this);
        this.onMakeCall = this.onMakeCall.bind(this);
        this.onDTMFInput = this.onDTMFInput.bind(this);
        this.getState = this.getState.bind(this);
        this.RegisterSIP = this.RegisterSIP.bind(this);
        this.UnregisterSIP = this.UnregisterSIP.bind(this);
        this.onHold = this.onHold.bind(this);
        this.onUnhold = this.onUnhold.bind(this);
        this.onMute = this.onMute.bind(this);
        this.onUnmute = this.onUnmute.bind(this);
        this.onAttendedTransfer = this.onAttendedTransfer.bind(this);
        this.onBlindTransfer = this.onBlindTransfer.bind(this);
        this.onAcceptTransfer = this.onAcceptTransfer.bind(this);
        this.onTransferHold = this.onTransferHold.bind(this);
        this.onTransferUnhold = this.onTransferUnhold.bind(this);
        this.onTransferMute = this.onTransferMute.bind(this);
        this.onTransferUnmute = this.onTransferUnmute.bind(this);
        this.onTransferHangup = this.onTransferHangup.bind(this);
        this.playRinger = this.playRinger.bind(this);
        this.stopRing = this.stopRing.bind(this);
        this.fetchDtmf = this.fetchDtmf.bind(this)
        this.pauseAgent = this.pauseAgent.bind(this)
    }

    setError(error) {
        openNotificationWithIcon(error);
    }

    setNotification(message) {
        openSuccessNotificationWithIcon(message);
    }
    // merge with solv3

    componentWillMount() {
        this.setState(
            {
                userAgent: new UserAgent({
                    authorizationUsername: this.state.authUser,
                    authorizationPassword: this.state.authPass,
                    transportOptions: {
                        server: `wss://${this.state.sipDomain}:${this.state.wssPort}/ws`,
                    },
                    uri: this.state.uri,
                    logLevel: "error",
                    delegate: {
                        onConnect: this.onConnect,
                        onDisconnect: this.onDisconnect,
                        onRegister: this.onRegister,
                        onInvite: this.onInvite,
                    },
                }),
            },
            () => {
                this.setState({ registerer: new Registerer(this.state.userAgent) });
            }
        );
        this.mediaElement = createRef();
        this.audioElement = null;
    }

    componentWillUnmount() {
        this.state.registerer.stateChange.removeListener(this.registerListener);
        this.state._session?.stateChange.removeListener(this.sessionListener);
    }

    playRinger() {
        this.audioElement?.play();
    }

    stopRing() {
        if (this.audioElement) {
            this.audioElement.pause();
            this.audioElement.currentTime = 0;
        }
    }

    fetchDtmf(number) {
        apiClient.post('api/agent/get-dtmf-input', { phone_number: number }).then((res) => {
            this.setState({ dtmfInput: res.data })
        }).catch((err) => {
            openNotificationWithIcon('error', "Unable to catch dtmf input")
        })
    }


    onInvite(invitation) {
        if (!this.state._session) {
            this.playRinger();
            const autoCallAnswer = sessionStorage.getItem('auto_call_answer') ?? 0;

            this.setState({ _session: invitation, isModalVisible: autoCallAnswer == 1 ? false : true }, () => {
                this.registerIncomingEvents();
            });
            this.setState({ dialedNumber: invitation.remoteIdentity.uri.user });
            this.setState({ incoming: true });
            this.props.setNumber(invitation.remoteIdentity.uri.user);
            //invitation.stateChange.addListener(this.sessionListener)

            this.fetchDtmf(invitation.remoteIdentity.uri.user);
            if (autoCallAnswer == 1) this.onCallAccept();

        } else {
            invitation
                .reject()
                .then((_) =>
                    openNotificationWithIcon(
                        `Incoming call from ${invitation.remoteIdentity.uri.user} rejected due to client busy.`
                    )
                )
                .catch((e) => console.log(e));
        }
    }

    onTransferHold() {
        const options = {
            sessionDescriptionHandlerModifiers: [Web.holdModifier],
        };
        this.state._transferredSession
            .invite(options)
            .then(() => this.setState({ isTransferHold: true }))
            .catch((err) => this.setError(err.message));
    }

    onTransferUnhold() {
        const options = {
            sessionDescriptionHandlerModifiers: [],
        };

        this.state._transferredSession
            .invite(options)
            .then(() => this.setState({ isTransferHold: false }))
            .catch((err) => this.setError(err.message));
    }

    onTransferMute() {
        let pc =
            this.state._transferredSession.sessionDescriptionHandler.peerConnection;
        let senders = pc.getSenders();
        if (senders.length) {
            senders.forEach((sender) => {
                if (sender.track) {
                    sender.track.enabled = false;
                    this.setState({ isTransferMute: true });
                }
            });
        }
    }

    onTransferUnmute() {
        let pc =
            this.state._transferredSession.sessionDescriptionHandler.peerConnection;
        let senders = pc.getSenders();
        if (senders.length) {
            senders.forEach((sender) => {
                if (sender.track) {
                    sender.track.enabled = true;
                    this.setState({ isTransferMute: false });
                }
            });
        }
    }

    onHold() {
        const options = {
            sessionDescriptionHandlerModifiers: [Web.holdModifier],
        };

        return this.state._session
            .invite(options)
            .then(() => this.setState({ isHold: true }))
            .catch((err) => this.setError(err.message));
    }

    onUnhold() {
        if (!this.state._session) {
            console.warn('Cannot unhold: session is null');
            return Promise.resolve();
        }

        const options = {
            sessionDescriptionHandlerModifiers: [],
        };

        return this.state._session
            .invite(options)
            .then(() => this.setState({ isHold: false }))
            .catch((err) => this.setError(err.message));
    }

    onMute() {
        if (!this.state._session) {
            console.warn('Cannot mute: session is null');
            return;
        }

        let pc = this.state._session.sessionDescriptionHandler.peerConnection;
        let senders = pc.getSenders();
        if (senders.length) {
            senders.forEach((sender) => {
                if (sender.track) {
                    sender.track.enabled = false;
                    this.setState({ isMute: true });
                }
            });
        }
    }

    onUnmute() {
        if (!this.state._session) {
            console.warn('Cannot unmute: session is null');
            return;
        }

        let pc = this.state._session.sessionDescriptionHandler.peerConnection;
        let senders = pc.getSenders();
        if (senders.length) {
            senders.forEach((sender) => {
                if (sender.track) {
                    sender.track.enabled = true;
                    this.setState({ isMute: false });
                }
            });
        }
    }

    onBlindTransfer(target) {
        const transferTarget = UserAgent.makeURI(
            `sip:${target}@${this.state.sipDomain}`
        );
        if (!transferTarget) {
            throw new Error("Failed to create transfer target URI.");
        }
        this.state._session
            .refer(transferTarget)
            .then(() => {
                this.setNotification("Call has been transferred");
                this.onEndCall();
            })
            .catch((error) => openNotificationWithIcon(error.message));
    }

    onTransferHangup() {
        if (this.state._transferredSession === null) return;
        switch (this.state._transferredSession.state) {
            case SessionState.Initial:
            case SessionState.Establishing:
                if (this.state._transferredSession instanceof Inviter) {
                    // An unestablished outgoing session
                    this.state._transferredSession
                        .cancel()
                        .then((res) => this.setNotification(res));
                } else {
                    // An unestablished incoming session
                    this.state._transferredSession.reject();
                }
                this.state._transferredSession.stateChange.removeListener(
                    this.sessionListener
                );
                this.setState({
                    _transferredSession: null,
                    isTransferMute: false,
                    isTransferHold: false,
                    isTransferConnected: false,
                    isBridged: false,
                });
                break;
            case SessionState.Established:
                // An established session
                this.state._transferredSession.bye();
                this.state._transferredSession.stateChange.removeListener(
                    this.sessionListener
                );
                this.setState({
                    _transferredSession: null,
                    isTransferMute: false,
                    isTransferHold: false,
                    isTransferConnected: false,
                    isBridged: false,
                });
                this.attachMedia();
                break;
            case SessionState.Terminating:
            case SessionState.Terminated:
                break;
            default:
                // Cannot terminate a session that is already terminated
                break;
        }
    }

    onAttendedTransfer(number) {
        // Attended Transfer
        /*transferSession.invite(options).then(r => {
                    this.state._session.refer(transferSession)
                    this.setState({ _transferredSession: transferSession })
                }).catch(err => {
                    this.setError(err.message)
                })*/

        // Attended transfer through aftab project
        const target = UserAgent.makeURI(`sip:${number}@${this.state.sipDomain}`);

        const transferSession = new Inviter(this.state.userAgent, target, {
            earlyMedia: true,
        });
        let constraints = {
            audio: true,
            video: false,
        };

        const options = {
            earlyMedia: true,
            sessionDescriptionHandlerOptions: {
                constraints,
            },
        };

        transferSession.stateChange.addListener((state) => {
            switch (state) {
                case SessionState.Initial:
                    break;
                case SessionState.Establishing:
                    this.setState({ isTransferConnected: true });
                    const remoteStream = new MediaStream();
                    transferSession.sessionDescriptionHandler.peerConnection
                        .getReceivers()
                        .forEach((receiver) => {
                            if (receiver.track) {
                                remoteStream.addTrack(receiver.track);
                            }
                        });
                    this.mediaElement.current.srcObject = remoteStream;
                    this.mediaElement.current.play();
                    break;
                case SessionState.Established:
                    break;
                case SessionState.Terminating:
                case SessionState.Terminated:
                    this.setState({ isTransferConnected: false, _transferSession: null });
                    this.mediaElement.current.srcObject = null
                    this.mediaElement.current.pause()
                    this.attachMedia()
                    break;
                default:
                    throw new Error("Unknown session state.");
            }
        });

        transferSession.invite(options).catch((e) => console.log(e));
        this.setState({ _transferredSession: transferSession });
    }

    onAcceptTransfer() {
        // Bridging all calls

        const receivedTracks = [];
        const sessionA = this.state._session;
        const sessionB = this.state._transferredSession;

        sessionA.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                receivedTracks.push(receiver.track);
            });

        sessionB.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                receivedTracks.push(receiver.track);
            });

        const context = new AudioContext();
        const mediaStream = new MediaStream();

        const sessions = [sessionA, sessionB];
        sessions.forEach((session) => {
            const mixedOutput = context.createMediaStreamDestination();
            session.sessionDescriptionHandler.peerConnection
                .getReceivers()
                .forEach((receiver) => {
                    receivedTracks.forEach((track) => {
                        mediaStream.addTrack(receiver.track);
                        if (receiver.track.id !== track.id) {
                            const sourceStream = context.createMediaStreamSource(
                                new MediaStream([track])
                            );
                            sourceStream.connect(mixedOutput);
                        }
                    });
                });

            session.sessionDescriptionHandler.peerConnection
                .getSenders()
                .forEach((sender) => {
                    const sourceStream = context.createMediaStreamSource(
                        new MediaStream([sender.track])
                    );
                    sourceStream.connect(mixedOutput);
                });

            session.sessionDescriptionHandler.peerConnection
                .getSenders()[0]
                .replaceTrack(mixedOutput.stream.getTracks()[0])
                .then((r) => {
                    this.setState({ isBridged: true }, () => {
                        if (this.state.isHold) this.onUnhold();
                    });
                    // console.log("track replaced");
                })
                .catch((e) => console.log(`error: ${e}`));
        });

        this.mediaElement.current.srcObject = mediaStream;
        this.mediaElement.current.play();
    }

    //function to create conference by mixing audio
    //sessions => array with JsSIP.RTCSessions calls
    //remoteAudioId => the ID of your <audio> element to play the received streams
    conference(sessions, remoteAudioId) {
        //take all received tracks from the sessions you want to merge
        var receivedTracks = [];
        sessions.forEach(function (session) {
            if (session !== null && session !== undefined) {
                session.connection.getReceivers().forEach(function (receiver) {
                    receivedTracks.push(receiver.track);
                });
            }
        });

        //use the Web Audio API to mix the received tracks
        var context = new AudioContext();
        var allReceivedMediaStreams = new MediaStream();

        sessions.forEach(function (session) {
            if (session !== null && session !== undefined) {
                var mixedOutput = context.createMediaStreamDestination();

                session.connection.getReceivers().forEach(function (receiver) {
                    receivedTracks.forEach(function (track) {
                        allReceivedMediaStreams.addTrack(receiver.track);
                        if (receiver.track.id !== track.id) {
                            var sourceStream = context.createMediaStreamSource(
                                new MediaStream([track])
                            );
                            sourceStream.connect(mixedOutput);
                        }
                    });
                });
                //mixing your voice with all the received audio
                session.connection.getSenders().forEach(function (sender) {
                    var sourceStream = context.createMediaStreamSource(
                        new MediaStream([sender.track])
                    );
                    sourceStream.connect(mixedOutput);
                });
                session.connection
                    .getSenders()[0]
                    .replaceTrack(mixedOutput.stream.getTracks()[0]);
            }
        });

        //play all received stream to you
        var remoteAudio = document.getElementById("remoteAudioId");
        remoteAudio.srcObject = allReceivedMediaStreams;
        var promiseRemote = remoteAudio.play();
        if (promiseRemote !== undefined) {
            promiseRemote
                .then((_) => {
                    console.log("playing all received streams to you");
                })
                .catch((error) => {
                    console.log(error);
                });
        }
    }

    /*onAcceptTransfer() {
            if(this.state._transferredSession) {
                console.log('this is transfer')
                const remoteStream = new MediaStream()
                this.state._session.sessionDescriptionHandler.peerConnection.getReceivers().forEach(receiver => {
                    if(receiver.track) {
                        remoteStream.addTrack(receiver.track)
                    }
                })
                this.state._transferredSession.sessionDescriptionHandler.peerConnection.getReceivers().forEach(receiver => {
                    if(receiver.track) {
                        remoteStream.addTrack(receiver.track)
                    }
                })
    
            }
        }*/

    //function to create conference by mixing audio
    //sessions => array with JsSIP.RTCSessions calls
    //remoteAudioId => the ID of your <audio> element to play the received streams
    /*onAcceptTransfer() {
            //take all received tracks from the sessions you want to merge
            const sessions = [this.state._session, this.state._transferredSession]
            let receivedTracks = [];
            sessions.forEach(function(session) {
                if(session !== null && session !== undefined) {
                    session.sessionDescriptionHandler.peerConnection.getReceivers().forEach(function(receiver) {
                        receivedTracks.push(receiver.track);
                    });
                }
            })
    
            console.log(receivedTracks)
    
            //use the Web Audio API to mix the received tracks
            let context = new AudioContext();
            let allReceivedMediaStreams = new MediaStream();
    
            sessions.forEach(function(session) {
                if(session !== null && session !== undefined) {
    
                    let mixedOutput = context.createMediaStreamDestination();
    
                    session.sessionDescriptionHandler.peerConnection.getReceivers().forEach(function(receiver) {
                        receivedTracks.forEach(function(track) {
                            allReceivedMediaStreams.addTrack(receiver.track);
                            if(receiver.track.id !== track.id) {
                                let sourceStream = context.createMediaStreamSource(new MediaStream([track]));
                                sourceStream.connect(mixedOutput);
                            }
                        });
                    });
                    //mixing your voice with all the received audio
                    session.sessionDescriptionHandler.peerConnection.getSenders().forEach(function(sender) {
                        let sourceStream = context.createMediaStreamSource(new MediaStream([sender.track]));
                        sourceStream.connect(mixedOutput);
                    });
                    session.sessionDescriptionHandler.peerConnection.getSenders()[0].replaceTrack(mixedOutput.stream.getTracks()[0]);
                }
            });
    
            //play all received stream to you
            this.mediaElement.current.srcObject = allReceivedMediaStreams
            this.mediaElement.current.play()
        }*/

    onConnect() {
        this.setState({ _connected: true });
    }

    onDisconnect(error) {
        // console.log(error);
        this.setState({ _connected: false });
    }

    pauseAgent(msg) {
        apiClient.post(`/api/agent/pause`, { reason: 'Not Submit Workcode Yet' || msg }).then(r => {
            console.log("User set to not ready after hangup", r.data);

            sessionStorage.setItem('workcode_submit', false);
            localStorage.setItem('workcode_submit', false);

            const callId = this.props.callId;
            if (callId) {
                sessionStorage.setItem('call_id', callId);
                localStorage.setItem('call_id', callId);
            }

            const callType = this.state._session instanceof Invitation ? "inbound" : "outbound";
            sessionStorage.setItem('current_call', callType);
            localStorage.setItem('current_call', callType);

        }).catch(e => console.log('Error setting agent to not ready:', e.response));
    }

    sessionListener(state) {
        // console.log(`Session state changed to ${state} from sessionListener`);
        this.setState({ sessionState: state });
        switch (state) {
            case SessionState.Initial:
                break;
            case SessionState.Establishing:
                this.attachMedia();
                if (!this.state._registered) openNotificationWithIcon('error', "Please register your self!")
                break;
            case SessionState.Established:
                this.stopRing();
                this.setState({ isConnected: true });
                this.props.setConnected(true);
                this.props.setIncomingCallAccepted(
                    this.state._session instanceof Invitation
                );
                this.props.setOutgoingCallAccepted(
                    this.state._session instanceof Inviter
                );
                break;
            case SessionState.Terminating:
            case SessionState.Terminated:
                this.stopRing();
                if (!this.state.isTransferConnected) {
                    this.cleanupMedia();
                }
                this.setState({
                    incoming: false,
                    isConnected: false,
                    _session: null,
                    isModalVisible: false,
                    isHold: false,
                });
                this.props.setConnected(false);
                this.props.setCallHangup(true);
                this.props.setNumber('');
                this.props.setIncomingCallAccepted(false);
                this.props.setOutgoingCallAccepted(false);
                this.props.setOutgoingDialed(false)

                this.setState({
                    isConnected: true
                })
                this.pauseAgent();
                this.props.setCallReminder(true)
                // this.props.setCallReportForm(true)
                break;
            default:
                throw new Error("Unknown session state.");
        }
    }

    incomingSessionListener(state) {
        // console.log(
        //     `Session state changed to ${state} from incomingSessionListener`
        // );
        this.setState({ sessionState: state });
        switch (state) {
            case SessionState.Initial:
                break;
            case SessionState.Establishing:
                break;
            case SessionState.Established:
                this.attachMedia();
                this.stopRing();
                this.setState({ isConnected: true });
                this.props.setConnected(true);
                this.props.setIncomingCallAccepted(
                    this.state._session instanceof Invitation
                );
                this.props.setOutgoingCallAccepted(
                    this.state._session instanceof Inviter
                );
                break;
            case SessionState.Terminating:
            case SessionState.Terminated:
                this.stopRing();
                if (!this.state.isTransferConnected) {
                    this.cleanupMedia();
                }
                this.props.setNumber("");
                this.setState({
                    incoming: false,
                    isConnected: false,
                    _session: null,
                    isModalVisible: false,
                    isHold: false,
                });
                this.props.setConnected(false);
                this.props.setCallHangup(true);
                this.props.setIncomingCallAccepted(false);
                this.props.setOutgoingCallAccepted(false);
                this.props.setFormVisible(false)
                break;
            default:
                throw new Error("Unknown session state.");
        }
    }

    registerListener(data) {
        switch (data) {
            case RegistererState.Registered:
                this.setState({ _registered: true });
                break;
            case RegistererState.Unregistered:
                this.setState({ _registered: false });
                break;
            case RegistererState.Terminated:
                this.setState({ _registered: false });
                break;
            case RegistererState.Initial:
            default:
                break;
        }
    }

    registerEvents() {
        if (this.state._session) {
            this.state._session.stateChange.addListener(this.sessionListener);
        } else {
            console.warn('Cannot register events: session is null');
        }
    }

    registerIncomingEvents() {
        if (this.state._session) {
            this.state._session.stateChange.addListener(this.incomingSessionListener);
        } else {
            console.warn('Cannot register incoming events: session is null');
        }
    }

    session() {
        return this.state._session;
    }

    getState() {
        return this.state._session?.state;
    }

    isConnected() {
        return this.state._connected;
    }

    isRegistered() {
        return this.state._registered;
    }

    RegisterSIP() {
        this.state.userAgent
            .start()
            .then(() => {
                this.state.userAgent.transport.stateChange.addListener((data) => {
                    switch (data) {
                        case TransportState.Disconnected:
                            console.log("transport disconnected");
                            break;
                        case TransportState.Connected:
                            console.log("transport connected");
                            break;
                        default:
                            break;
                    }
                });
                // User Agent has started
                this.state.registerer
                    .register()
                    .then((res) => {
                        this.state.registerer.stateChange.addListener(
                            this.registerListener
                        );
                    })
                    .catch((error) => this.setError(error.message));
            })
            .catch((error) => this.setError(error.message));
    }

    UnregisterSIP() {
        this.state.registerer
            .unregister()
            .then((res) => console.log(res))
            .catch((error) => this.setError(error.message));
    }

    // Origional Code
    // onMakeCall(number) {
    //     const target = UserAgent.makeURI(`sip:${number}@${this.state.sipDomain}`);
    //     const inviter = new Inviter(this.state.userAgent, target, {
    //         earlyMedia: true,
    //     });
    //     let constraints = {
    //         audio: true,
    //         video: false,
    //     };

    //     const options = {
    //         earlyMedia: true,
    //         sessionDescriptionHandlerOptions: {
    //             constraints,
    //             iceGatheringTimeout: 100,
    //         },
    //     };
    //     inviter
    //         .invite(options)
    //         .then((res) => console.log(res))
    //         .then(res => this.props.setOutgoingDialed(true))
    //         .catch((error) => this.setError(error.message));
    //     this.setState({ _session: inviter }, () => this.registerEvents());
    //     this.props.setNumber(number);
    // }

    onMakeCall = async (number) => {
        sessionStorage.setItem("setDialerSpin", true)
        const token = sessionStorage.getItem('agent_token');
        if (!token) {
            openNotificationWithIcon("error", "Authentication error. Please log in again.");
            sessionStorage.setItem("setDialerSpin", false)
            return;
        }
        if (this.props.user?.type === 'Inbound') {
            openNotificationWithIcon('error', 'Outbound calls are not allowed for inbound agents');
            return;
        }
        try {
            // Check prepaid balance
            const balanceResponse = await apiClient.get('api/prepaid-bil', {
                headers: { Authorization: `Bearer ${token}` }
            });
            const remainingAmount = balanceResponse.data.prepaid.remaining_amount;
            // console.log("remainingAmount is here " + balanceResponse.data.prepaid.remaining_amount);

            if (remainingAmount <= 0) {
                openNotificationWithIcon("error", "Insufficient balance. Cannot make a call.");
                sessionStorage.setItem("setDialerSpin", false)
                return;
            }

            // Check available minutes
            // const checkRemainingData = await apiClient.get('api/check-available-minutes', {
            //     headers: { Authorization: `Bearer ${token}` }
            // });

            // const availableMinutes = checkRemainingData.data.remaining_minutes;

            // if (checkRemainingData.data.status == "warning") {
            //     console.log(`checkRemainingData : ${JSON.stringify(checkRemainingData)}`);
            //     openNotificationWithIcon("error", `${checkRemainingData.data.message}`);
            //     // sessionStorage.setItem("setDialerSpin", false);
            // }


            // If balance and minutes are sufficient, proceed with the call
            const target = UserAgent.makeURI(`sip:${number}@${this.state.sipDomain}`);
            const inviter = new Inviter(this.state.userAgent, target, {
                earlyMedia: true,
            });

            let constraints = {
                audio: true,
                video: false,
            };

            const options = {
                earlyMedia: true,
                sessionDescriptionHandlerOptions: {
                    constraints,
                    iceGatheringTimeout: 100,
                },
            };

            inviter
                .invite(options)
                .then((res) => {
                    // console.log(res);
                    this.props.setOutgoingDialed(true); // Set outgoing call state
                })
                .catch((error) => {
                    this.setError(error.message);
                });

            this.setState({ _session: inviter }, () => this.registerEvents());
            this.props.setNumber(number); // Set the number to call
        } catch (error) {
            openNotificationWithIcon(`Error making API request: ${error}`);
        }
    };

    onDTMFInput(number) {
        const options = {
            requestOptions: {
                body: {
                    contentDisposition: "render",
                    contentType: "application/dtmf-relay",
                    content: `Signal=${number}\r\nDuration=1000`,
                },
            },
        };

        if (this.state._session) {
            this.state._session.info(options);
        } else {
            console.warn('Cannot send DTMF: session is null');
        }
    }

    onEndCall() {
        this.stopRing();
        if (this.state._session === null) return;
        switch (this.state._session.state) {
            case SessionState.Initial:
            case SessionState.Establishing:
                if (this.state._session instanceof Inviter) {
                    // An unestablished outgoing session
                    this.state._session.cancel().then((res) => this.setNotification(res));
                } else {
                    // An unestablished incoming session
                    this.state._session.reject();
                }
                this.setState({ _session: null, isModalVisible: false });
                break;
            case SessionState.Established:
                // An established session
                this.state._session.bye().then((res) => console.log(res));
                this.setState({ _session: null, isModalVisible: false, isHold: false });
                this.props.setNumber('');
                break;
            case SessionState.Terminating:
            case SessionState.Terminated:
                this.state._session.stateChange.removeListener(this.sessionListener);
                this.setState({
                    _session: null,
                    isModalVisible: false,
                    isMute: false,
                    isHold: false,
                });
                this.props.setFormVisible(false)
                break;
            default:
                // Cannot terminate a session that is already terminated
                break;
        }
    }

    onCallAccept() {
        let constraints = {
            audio: true,
            video: false,
        };

        const options = {
            sessionDescriptionHandlerOptions: {
                constraints,
                iceGatheringTimeout: 100,
            },
        };
        this.state._session.accept(options);
        //this.state._session.accept(options).then((res) => console.log(res));
        //this.registerEvents()
        this.setState({ isModalVisible: false });
        // this.setState({ isCallAccepted: true });
        this.props.setIsCallAccepted(true);
    }

    attachMedia() {
        const remoteStream = new MediaStream();
        this.state._session.sessionDescriptionHandler.peerConnection
            .getReceivers()
            .forEach((receiver) => {
                if (receiver.track) {
                    remoteStream.addTrack(receiver.track);
                }
            });
        this.mediaElement.current.srcObject = remoteStream;
        this.mediaElement.current.play();
    }

    cleanupMedia() {
        this.mediaElement.current.srcObject = null;
        this.mediaElement.current.pause();
    }

    render() {
        let timer = this.props.time;
        let breakTime = this.props.break;

        const cardHeader = (
            <Row justify="space-between" align="middle">
                <Col>
                    <Tag
                        color={this.state._registered ? "#87d068" : "#cd201f"}
                        icon={<PhoneOutlined />}
                    >
                        {this.state._registered ? "online" : "offline"}
                    </Tag>
                </Col>
                <Col>
                    <Tag icon={<UserOutlined />}>
                        {this.props?.authUser}
                    </Tag>
                    <Tag icon={<ClockCircleOutlined />}>
                        <TimerComponent
                            time={this.props.time}
                            active={this.props.queueLoggedIn}
                            title={"Login Time"}
                        />
                    </Tag>
                    <Tag icon={<ClockCircleOutlined />}>
                        <TimerComponent
                            time={this.props.break}
                            active={this.props.onBreak}
                            title={"Break Time"}
                        />
                    </Tag>
                </Col>
                <Col>
                    <Tag
                        color={this.state._connected ? "#87d068" : "#cd201f"}
                        icon={<CodeSandboxOutlined />}
                    >
                        {this.state._connected ? "online" : "offline"}
                    </Tag>
                </Col>
            </Row>
        );

        return (
            <>
                <Card title={cardHeader} size="small">
                    <Typography style={{ textAlign: "center" }}>
                        <Typography.Title level={4}>
                            Welcome {sessionStorage.getItem("agent_username")}!
                        </Typography.Title>
                        {!this.state._registered ? (
                            <Typography.Title level={4}>
                                Click on <CodeOutlined /> icon to register your phone.
                            </Typography.Title>
                        ) : (
                            <>
                                <Typography.Title level={4}>
                                    Your phone is{" "}
                                    <span style={{ color: "green", textDecoration: "underline" }}>
                                        registered
                                    </span>
                                    . Click on this <StopOutlined /> icon to unregister your
                                    phone.
                                </Typography.Title>
                                <Typography.Title level={4}>
                                    Click on <ClusterOutlined /> icon to login into queue(s){" "}
                                    <span style={{ color: "green", textDecoration: "underline" }}>
                                        [{this.state.queues}]
                                    </span>{" "}
                                    and click on <DeliveredProcedureOutlined /> to logout.
                                </Typography.Title>
                            </>
                        )}
                    </Typography>
                    <audio ref={this.mediaElement} />
                    <audio ref={(e) => (this.audioElement = e)} src={ring} loop={true} />
                </Card>
                <IncomingCall
                    isHold={this.state.isHold}
                    isMute={this.state.isMute}
                    unholdCall={this.onUnhold}
                    unmuteCall={this.onUnmute}
                    holdCall={this.onHold}
                    muteCall={this.onMute}
                    endCall={this.onEndCall}
                    number={this.state.dialedNumber}
                    isConnected={this.state.isConnected}
                    incoming={this.state.incoming}
                    blindTransfer={this.onBlindTransfer}
                    attendedTransfer={this.onAttendedTransfer}
                    onAcceptTransfer={this.onAcceptTransfer}
                    sessionState={this.state.sessionState}
                    isTransferMute={this.state.isTransferMute}
                    isTransferHold={this.state.isTransferHold}
                    onTransferMute={this.onTransferMute}
                    onTransferUnmute={this.onTransferUnmute}
                    onTransferUnhold={this.onTransferUnhold}
                    onTransferHold={this.onTransferHold}
                    isTransferConnected={this.state.isTransferConnected}
                    onTransferHangup={this.onTransferHangup}
                    isBridged={this.state.isBridged}
                    callId={this.props.callId}
                    queue={this.props.queues}
                    authUser={this.props.authUser}
                    dtmfInput={this.state.dtmfInput}
                />
                <IncomingModal
                    number={this.state.dialedNumber}
                    isModalVisible={this.state.isModalVisible}
                    isCallAccepted={this.props.isCallAccepted}
                    setIsCallAccepted={this.props.setIsCallAccepted}
                    onOk={this.onCallAccept}
                    onCancel={this.onEndCall}
                    settings={this.props.settings}
                    setIncomingCallAccepted={this.props.setIncomingCallAccepted}
                    formWidgetDataOnCallAcceptance={
                        this.props.formWidgetDataOnCallAcceptance
                    }
                    setFormWidgetDataOnCallAcceptance={
                        this.props.setFormWidgetDataOnCallAcceptance
                    }

                    dtmfInput={this.state.dtmfInput}
                />
                <DialerMenu
                    visible={this.props.dialerVisible}
                    setVisible={this.props.setDialerVisible}
                    onClose={this.props.onClose}
                    isHold={this.state.isHold}
                    isMute={this.state.isMute}
                    unholdCall={this.onUnhold}
                    unmuteCall={this.onUnmute}
                    holdCall={this.onHold}
                    muteCall={this.onMute}
                    endCall={this.onEndCall}
                    makeCall={this.onMakeCall}
                    dtmfInput={this.onDTMFInput}
                    // number={this.state.dialedNumber}
                    setNumber={this.props.setNumber}
                    isConnected={this.state.isConnected}
                    blindTransfer={this.onBlindTransfer}
                    attendedTransfer={this.onAttendedTransfer}
                    onAcceptTransfer={this.onAcceptTransfer}
                    sessionState={this.state.sessionState}
                    isTransferMute={this.state.isTransferMute}
                    isTransferHold={this.state.isTransferHold}
                    onTransferMute={this.onTransferMute}
                    onTransferUnmute={this.onTransferUnmute}
                    onTransferUnhold={this.onTransferUnhold}
                    onTransferHold={this.onTransferHold}
                    isTransferConnected={this.state.isTransferConnected}
                    onTransferHangup={this.onTransferHangup}
                    isBridged={this.state.isBridged}
                    campaignStatus={this.props.campaignStatus}
                    campaignPauseStatus={this.props.campaignPauseStatus}
                    user={this.props.user}
                    authUser={this.props.authUser}

                    number={this.props.number}
                    redialClick={this.props.redialClick}
                    setRedialClick={this.props.setRedialClick}
                    redialId={this.props.redialId}
                    setRedialId={this.props.setRedialId}
                    showNotification={this.props.showNotification}
                    setShowNotification={this.props.setShowNotification}
                />

            </>
        );
    }
}

