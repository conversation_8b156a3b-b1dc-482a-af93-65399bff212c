import {
    But<PERSON>,
    Card,
    Col,
    Descriptions,
    Dropdown,
    Form,
    Image,
    Input,
    Layout,
    Menu,
    Modal,
    notification,
    Pagination,
    Row,
    Space,
    Spin,
    Table,
} from "antd";
import React, {
    useState,
    useRef,
    useEffect,
    useContext,
    useLayoutEffect,
} from "react";
import {
    BoxPlotOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ClusterOutlined,
    CodeOutlined,
    ContainerOutlined,
    DeliveredProcedureOutlined,
    EditOutlined,
    EyeInvisibleOutlined,
    FormatPainterOutlined,
    FormOutlined,
    LoginOutlined,
    LogoutOutlined,
    SearchOutlined,
    SettingOutlined,
    SnippetsOutlined,
    StopOutlined,
    MessageOutlined,
    MailOutlined,
    PhoneOutlined,
    FileTextOutlined
} from "@ant-design/icons";
import DialerAccount from "../dialer/DialerAccount";
import SIPModule from "./SIPModule";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
    fetchACDRs,
    fetchAgentStatusInQueue,
    fetchAStats,
    fetchChannelId,
    fetchIsReady,
    fetchLast5Calls,
    fetchLoginQueue,
    fetchLogoutQueue,
    fetchNotReadyQueue,
    fetchPauseReasons,
    fetchQStats,
    fetchReadyQueue,
    fetchWorkcodes,
} from "../config/queries";
import openSuccessNotificationWithIcon from "../components/Message";
import openNotificationWithIcon from "../components/Notification";
import Widget, { WidgetScript } from "../components/Widget";
import CallDetailWidget from "../components/CallDetailWidget";
import { useStorageState } from "react-storage-hooks";
import Workcode from "../components/Workcode";
import { postNotReady, postWorkcode, postUnPause } from "../config/mutations";
import NotReady from "../components/NotReady";
import AgentStatusWidget from "../components/AgentStatusWidget";
import CampaignWidget from "../components/CampaignWidget";
import { FormWidget } from "../components/FormWidget";
import { useDispatch, useSelector } from "react-redux";
import { getCallId } from "../Actions/CallAction";
import { WorkcodeWidget } from "../components/WorkcodeWidget";
import { getOutgoingCallId } from "../Actions/OutgoingCallAction";
import Last5CallHistory from "../components/Last5CallHistory";
import { fetchAgentScript } from "../Actions/ScriptActions";
import apiClient from "../config/apiClient";
import { handleError } from "../Shared/handleError";
import { timerFunc } from "../Shared/timerFunc";
import { logout } from "../config/routes";
import { QuestionCircleOutlined, UserSwitchOutlined } from "@ant-design/icons";
import logo from './../logo-lg.png';
import { useHistory } from "react-router-dom";
import initializeEcho from '../config/echo';
import Pusher from 'pusher-js';
import { SMSModule } from "../components/SMSModule";
import { MyModal } from "../components/MailBox";
import { Session } from "sip.js";
import { CallReminder } from "../components/CallReminder";
import { CallReportForm } from "../components/CallReportForm";
import { ScheduleCallBackTable } from "../components/ScheduleCallBackTable";

export default function DialerLayout(props) {
    const queryClient = useQueryClient();
    const history = useHistory()
    const userId = sessionStorage.getItem('id');
    const echo = initializeEcho(sessionStorage.getItem('agent_token'));
    const streamURL = `${process.env.REACT_APP_baseURL}/api/getcallback-voicemail`;

    const [queueStats, setQueueStats, writeErrorQueue] = useStorageState(
        localStorage,
        "queue-stats",
        false
    );
    const [agentStats, setAgentStats, writeErrorAgent] = useStorageState(
        localStorage,
        "agent-stats",
        false
    );
    const [cdrStats, setCdrStats, writeErrorCdr] = useStorageState(
        localStorage,
        "cdr-stats",
        false
    );
    const [lastCallStats, setLastCallStats, writeErrorLastCall] = useStorageState(
        localStorage,
        "last-calls-stats",
        false
    );
    const [aaStats, setAaStats, writeErrorAa] = useStorageState(
        localStorage,
        "aa-stats",
        false
    );
    const [refetchInterval, setRefetchInterval, writeErrorRefetchInterval] =
        useStorageState(localStorage, "refetch-interval", 5);

    const formState = useSelector((state) => state.FormWidgetReducer);




    // State vars
    const [callHistory, setCallHistory] = useState([]);
    const [number, setNumber] = useState();
    const [connected, setConnected] = useState(false);
    const [callHangup, setCallHangup] = useState(false);
    const [channelId, setChannelId] = useState(false);
    const [notReadyVisible, setNotReadyVisible] = useState(false);
    const [notReadyReason, setNotReadyReason] = useState(false);
    const [showCampaign, setShowCampaign] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isReady, setIsReady] = useState(false);
    const [readyButtonDisable, setReadyButtonDisable] = useState(false);
    const [campaignStatus, setCampaignStatus] = useState(false);
    const [campaignPauseStatus, setCampaignPauseStatus] = useState(false);
    const [activeCampaign, setActiveCampaign] = useState(false);
    const [isCallAccepted, setIsCallAccepted] = useState(false);
    const [cdrPage, setCdrPage] = useState(false);
    const [filteredNumber, setFilteredNumber] = useState();
    //   const [isCallAccepted, setIsCallAccepted] = useState(false);

    //   const [formWidgetDataOnCallAcceptance, setFormWidgetDataOnCallAcceptance] =
    //     useState();

    const queueState = useSelector((state) => state.QueueReducer);
    const scriptState = useSelector((state) => state.ScriptReducer);
    const callState = useSelector((state) => state.CallReducer);
    const outCallState = useSelector((state) => state.OutgoingCallReducer)

    const [queueLoggedIn, setQueueLoggedIn] = useState(false);
    const [onBreak, setOnBreak] = useState(false);
    const [timer, setTimer] = useState(0);
    const [breakTimer, setBreakTimer] = useState(0);
    const [loggedin, setLoggedin] = useState(sessionStorage.getItem('loggedin') === "true")
    const [username, setUsername] = useState(sessionStorage.getItem('agent_username'))
    const [error,] = useState(false)
    const [loading, setLoading] = useState(false)
    const [passwordModal, setPasswordModal] = useState(false)
    let [form] = Form.useForm()
    const [smsVisible, setSmsVisible] = useState(false);

    const [showModal, setShowModal] = useState(false);

    const openModal = () => setShowModal(true);
    const closeModal = () => setShowModal(false);


    // const [MailBox, setMailBox] = useState(false);


    // Form state
    const [forms, setForms] = useState([]);
    const [selectedFormfromMenu, setSelectedFormfromMenu] = useState();
    const [formVisible, setFormVisible] = useState(false);
    const [outgoingDialed, setOutgoingDialed] = useState(false)

    // Workcode State
    const [workcodeVisible, setWorkcodeVisible] = useState(false);


    // Workcode State
    const [callReminder, setCallReminder] = useState(false);
    const [callReportForm, setCallReportForm] = useState(false);



    // Incoming call state
    const [incomingCallAccepted, setIncomingCallAccepted] = useState(false);
    const [notReadyStatus, setNotReadyStatus] = useState(false);

    const dispatch = useDispatch();

    // Outgoing Call State
    const [outgoingCallAccepted, setOutgoingCallAccepted] = useState(false);

    useEffect(() => {
        if (incomingCallAccepted) dispatch(getCallId());
    }, [incomingCallAccepted]);

    useEffect(() => {
        if (formState.forms) setForms(formState.forms);
    }, [formState.forms, forms]);

    // checking for agent logged in

    // const [paginate, setPaginate] = useState({
    //     current: 1,
    //     pageSize: 15,
    //     total: 0,
    //     // total: 15,
    // });

    const [initialLoad, setInitialLoad] = useState(true);
    const [paginate, setPaginate] = useState({
        abandonCalls: {
            current: 1,
            pageSize: 15,
            total: 0
        },
        callbackRequests: {
            current: 1,
            pageSize: 15,
            total: 0
        }
    });

    // useEffect(() => {
    //     let interval = setInterval(() => {
    //         apiClient.post("/api/agent/is-login").then((res) => setQueueLoggedIn(res.data));
    //     }, 5000);

    //     return () => {
    //         clearInterval(interval);
    //     };
    // }, []);

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-login-channel.${userId}`);

        channel.listen(".agent-panel-login", (e) => {
            // console.log(`agent-panel-login : ${e.data.status}`)
            setQueueLoggedIn(e.data.status);
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-login");
        };
    }, [userId]);


    const { current, pageSize } = paginate;
    const [data, setData] = useState([])

    // useEffect(() => {
    //     // const pagination = {
    //     //     current: current,
    //     //     pageSize: pageSize
    //     // }

    //     // getAbandonData(pagination);

    //     // Listen for WebSocket events
    //     const channel = echo.private(`agent-panel-getabandoncallreport-channel.${userId}-${pagination.current}-${pagination.pageSize}`);

    //     channel.listen(".agent-panel-abandoncallreport", (e) => {
    //         console.log("Trigger Socket abandoncallreport " + JSON.stringify(e.data.total));
    //         setData(e.data.data.data);
    //         setPaginate(prev => ({ ...prev, total: e.data.total }));
    //     });

    //     // Cleanup subscription on component unmount
    //     return () => {
    //         channel.stopListening(".agent-panel-abandoncallreport");
    //     };

    // }, [userId, current, pageSize]);


    const handleStatusUpdate = (values) => {
        const payload = {
            callid: values.callid,
            status: true,
            userid: userId,
            current,
            pageSize
        }

        apiClient.put('api/report/update-abandoncall-status', payload).then((r) => {
            // const pagination = {
            //     current: current,
            //     pageSize: pageSize,
            // }
            // getAbandonData(pagination)

            openSuccessNotificationWithIcon(r?.data?.message);

            if (r?.data?.message) {
                setAbandoncall(prev => ({
                    ...prev,
                    data: {
                        ...prev.data,
                        data: prev.data.data.filter(item =>
                            item.callid !== values.callid ||
                            item.date !== values.date ||
                            item.time !== values.time
                        )
                    }
                }));
            }
        }).catch((e) => {
            console.log("Error", e)
            openNotificationWithIcon('error', error.response?.data?.error || 'Failed to update status');
        })
    }

    const updateCallbackRequestStatus = async (data) => {
        try {
            const response = await apiClient.patch(`api/callback-requests/${data.id}/status`, {
                userId: userId,
                status: true,
            });

            openSuccessNotificationWithIcon(response.data.message);

            if (response.data?.data?.id > 0) {
                setCallbackRequest(prev => ({
                    ...prev,
                    data: {
                        ...prev.data,
                        data: prev.data.data.filter(item => item.id !== data.id)
                    }
                }));
            }
        } catch (error) {
            console.error('Failed to update status:', error.response?.data || error.message);
            openNotificationWithIcon('error', error.response?.data?.message || 'Failed to update status');
        }
    };

    // useEffect(() => {

    //     const abandonInterval = setInterval(() => {
    //         getAbandonData()
    //     }, 5000)
    //     return () => clearInterval(abandonInterval);
    // }, [])

    const getAbandonData = async (payload) => {
        const response = await apiClient.post('api/report/abandonCallReport', payload)

        if (response.data) {
            setData(response?.data?.data)
        }
    }


    const settingsProps = {
        queueStats,
        setQueueStats,
        agentStats,
        setAgentStats,
        cdrStats,
        setCdrStats,
        lastCallStats,
        setLastCallStats,
        aaStats,
        setAaStats,
        refetchInterval,
        setRefetchInterval,
    };

    const incomingCallProps = {
        incomingCallAccepted,
        setIncomingCallAccepted,
    };

    const outgoingCallProps = {
        outgoingCallAccepted,
        setOutgoingCallAccepted,
    };

    useEffect(() => {
        if (outgoingDialed) dispatch(getOutgoingCallId());
    }, [outgoingDialed]);

    // useEffect(() => {
    //     if (queueState.queues && queueState.queues.length > 0)
    //         dispatch(fetchAgentScript(queueState.queues, userId));
    // }, [queueState.queues]);

    const [queueScript, setQueueScript] = useState([]);
    const [scriptLoaded, setScriptLoaded] = useState(false);
    const hasFetchedScript = useRef(false);

    const fetchQueueScript = () => {
        apiClient
            .post("/api/get-script-by-queue", { queue: queueState.queues })
            .then((r) => {
                setQueueScript(r.data);
                setScriptLoaded(true);
            });
    };

    useEffect(() => {
        if (!hasFetchedScript.current && queueState.queues?.length > 0) {
            fetchQueueScript();
            hasFetchedScript.current = true;
        }
    }, [queueState.queues]);

    useEffect(() => {
        fetchLast5Calls(number)
            .then((r) => setCallHistory(r.data))
            .catch((e) => console.log(e));
    }, [number]);

    // Mutations
    const notReadyMutation = useMutation(postNotReady, {
        onSuccess: (r, variables) => {

            setNotReadyVisible(false);
            setIsLoading(false);
            setReadyButtonDisable(false);
            queryClient
                .invalidateQueries("isReadyQuery")
                .catch((e) => console.log(e));
            queryClient.invalidateQueries("aStats").catch((e) => console.log(e));
            switch (r?.data?.response) {
                case "Success":
                    setNotReadyStatus(false)
                    openSuccessNotificationWithIcon(r?.data?.message);
                    break;
                case "Error":
                    setNotReadyStatus(true)
                    openNotificationWithIcon("error", r?.data?.message);
                    break;
                default:
                    break;
            }
        },
        onError: (error, variables) =>
            openNotificationWithIcon(error?.response?.data),
    });

    const unPauseMutation = useMutation(postUnPause, {
        onSuccess: (r, variables) => {
            setIsReady(false);
            queryClient
                .invalidateQueries("isReadyQuery")
                .catch((e) => console.log(e));
            switch (r.response) {
                case "Success":
                    openSuccessNotificationWithIcon(r.message);
                    break;
                case "Error":
                    openNotificationWithIcon(r.message);
                    break;
                default:
                    break;
            }
        },
        onError: (error, variables) => {
            openNotificationWithIcon(error?.response?.data);
            setIsReady(false);
        },
    });

    const workcodeMutation = useMutation(postWorkcode, {
        onSuccess: (response) => {
            openSuccessNotificationWithIcon(response.data);
            setChannelId(null);
        },
        onError: (error) => openNotificationWithIcon(error.response.data.message),
    });

    const options = {
        refetchInterval: 5000,
        refetchOnReconnect: true,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
    };

    const qStatsQuery = useQuery("qStats", fetchQStats, {
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled: queueStats,
        refetchInterval: parseInt(refetchInterval) * 1000,
    });
    const aStatsQuery = useQuery("aStats", fetchAStats, {
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled: agentStats,
        refetchInterval: parseInt(refetchInterval) * 1000,
    });
    const aCDRQuery = useQuery(["aCDR", cdrPage, filteredNumber], fetchACDRs, {
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled: cdrStats,
        refetchInterval: parseInt(refetchInterval) * 1000,
    });

    // const agentScript = useQuery('agentScript', fetchAgentsScript, options)


    // const workcodeQuery = useQuery("workCode", fetchWorkcodes, options);
    const [workcodeQuery, setWorkcodeQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-workcode-channel.${userId}`);

        setWorkcodeQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-workcode", (e) => {
            setWorkcodeQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-workcode");
        };
    }, [userId]);


    // const pauseReasonQuery = useQuery("pauseReason", fetchPauseReasons, options);
    const [pauseReasonQuery, setPauseReasonQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-pause-reason-channel.${userId}`);

        setPauseReasonQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-pause-reason", (e) => {
            setPauseReasonQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-pause-reason");
        };
    }, [userId]);


    // const agentStatusInQueue = useQuery(
    //     "agentStatusInQueue",
    //     fetchAgentStatusInQueue,
    //     {
    //         ...options,
    //         enabled: queueLoggedIn,
    //     }
    // );

    const [agentStatusInQueue, setAgentStatusInQueue] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        if (!queueLoggedIn) return;

        // Listen for WebSocket events
        const channel = echo.private(`agent-pannel-agent-status-channel.${userId}`);

        setAgentStatusInQueue((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-agent-status", (e) => {
            setAgentStatusInQueue({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.status,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-agent-status");
        };
    }, [userId, queueLoggedIn]);

    const getChannelIdQuery = useQuery("getChannelId", fetchChannelId, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });

    const loginQueueQuery = useQuery("loginQueue", fetchLoginQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const logoutQueueQuery = useQuery("logoutQueue", fetchLogoutQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const readyQueueQuery = useQuery("readyQueue", fetchReadyQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const notReadyQueueQuery = useQuery("notReadyQueue", fetchNotReadyQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });

    // const isReadyQuery = useQuery("isReadyQuery", fetchIsReady, options);
    const [isReadyQuery, setIsReadyQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-isready-channel.${userId}`);

        setIsReadyQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-isready", (e) => {
            setIsReadyQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.status,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-isready");
        };
    }, [userId]);


    // abandoncall channel start
    const [abandoncall, setAbandoncall] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });



    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-getabandoncallreport-channel.${userId}`);

        setAbandoncall((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-abandoncallreport", (e) => {
            // console.log(`Socket agent-panel-abandoncallreport : ${JSON.stringify(e)}`);
            setAbandoncall({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });

            setPaginate(prev => ({
                ...prev,
                abandonCalls: {
                    total: e.data.total,
                    current: e.data.current,
                    pageSize: e.data.pageSize
                }
            }));
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-abandoncallreport");
        };
    }, [userId]);
    // abandoncall channel end

    // call back request channel start
    const [callbackRequest, setCallbackRequest] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    const [redialClick, setRedialClick] = useState(false)
    const [redialId, setRedialId] = useState(null)
    const [showNotification, setShowNotification] = useState(true);

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-getcallbackrequest-channel.${userId}`);

        setCallbackRequest((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-getcallbackrequest", (e) => {
            // console.log(`Socket agent-panel-getcallbackrequest : ${JSON.stringify(e.data)}`);
            setCallbackRequest({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });

            setPaginate(prev => ({
                ...prev,
                callbackRequests: {
                    total: e.data.total,
                    current: e.data.current,
                    pageSize: e.data.pageSize
                }
            }));
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-getcallbackrequest");
        };
    }, [userId]);
    // call back request channel end

    useEffect(() => {
        if (initialLoad) {
            handlePaginationChange('abandonCalls')(1, paginate.abandonCalls.pageSize);
            handlePaginationChange('callbackRequests')(1, paginate.callbackRequests.pageSize);
            setInitialLoad(false);
        }
    }, [initialLoad]);

    const handlePaginationChange = (tableKey) => (page, pageSize) => {
        if (tableKey == "abandonCalls") {
            setAbandoncall(prev => ({
                ...prev,
                isLoading: true,
                isFetching: true,
                isIdle: false
            }));
        }
        else if (tableKey == "callbackRequests") {
            setCallbackRequest(prev => ({
                ...prev,
                isLoading: false,
                isFetching: false
            }));
        }

        apiClient.post('/api/update-pagination', {
            table: tableKey,
            current: page,
            pageSize: pageSize
        }).then(() => {
            setPaginate(prev => ({
                ...prev,
                [tableKey]: {
                    ...prev[tableKey],
                    current: page,
                    pageSize: pageSize
                }
            }));
        });
    };


    const [dialerVisible, setDialerVisible] = useState(false);
    const [dialerAccountVisible, setDialerAccountVisible] = useState(false);
    let sipModule = null;

    useEffect(() => {
        if (campaignStatus) {
            logoutQueue();
        }
    }, [campaignStatus]);

    useEffect(() => {
        if (
            (outgoingDialed || incomingCallAccepted) &&
            forms.some((record) => record.default === 1)
        ) {
            setFormVisible(true);
        }
    }, [outgoingDialed, incomingCallAccepted, forms]);

    useEffect(() => {
        if (isReadyQuery?.data) {
            setReadyButtonDisable(true)
        }
    }, [isReadyQuery?.data])

    // isReadyQuery.data?.length > 0
    // ? !isReadyQuery.data[0]
    // : !isReadyQuery.data

    useEffect(() => {
        // find the record with default set to 1
        const recordWithDefault = forms.find((record) => record.default === 1);
        // get the id of the record with default set to 1
        const id = recordWithDefault ? recordWithDefault.id : null;
        if (outgoingDialed || incomingCallAccepted && number && id) {
            apiClient
                .get(`api/getFormData?phone_number=${number}&form_id=${id}`)
                .then((res) => {
                    setSelectedFormfromMenu(res.data);
                });
        } else if (formVisible && !number) {
            apiClient.get(`api/getFormData?form_id=${id}`).then((res) => {
                setSelectedFormfromMenu(res.data);
            });
        }
    }, [outgoingDialed, incomingCallAccepted]);

    const alertUser = (e) => {
        if (agentStatusInQueue.data) {
            logoutQueueQuery.refetch();
        }
    };

    const showDialerDrawer = () => {
        setDialerVisible(true);
    };

    const onDialerClose = () => {
        setDialerVisible(false);
    };

    const onDialerAccountClose = () => {
        setDialerAccountVisible(false);
    };

    const showDialerAccount = () => {
        setDialerAccountVisible(true);
    };

    const registerSIP = () => {
        sipModule.RegisterSIP();
    };

    const unregisterSIP = () => {
        sipModule.UnregisterSIP();
    };

    const refreshQStats = () => {
        qStatsQuery.refetch();
    };

    const refreshAStats = () => {
        aStatsQuery.refetch();
    };

    function setZeros(i) {
        if (i < 10) return "0" + i;
        return i;
    }

    /**
     * Login to queue function
     */
    const loginQueue = () => {
        // Login queue
        loginQueueQuery
            .refetch()
            .then((r) => {
                if (r.isError) {
                    openNotificationWithIcon("error", r.error?.response?.data);
                } else {
                    switch (r.data.response) {
                        case "Success":
                            openSuccessNotificationWithIcon("success", r.data.message);
                            fetchTime();
                            break;
                        case "Error":
                            openNotificationWithIcon("error", r.data.message);
                            break;
                        default:
                            break;
                    }
                }
            })
            .then(() => queryClient.invalidateQueries("agentStatusInQueue"))
            .catch((e) => console.log(e));
    };

    /**
     * Logout from queue
     */
    const logoutQueue = () => {
        // Logout queue
        logoutQueueQuery
            .refetch()
            .then((r) => {
                if (r.isError) {
                    openNotificationWithIcon(r.error?.response?.data);
                } else {
                    switch (r.data.response) {
                        case "Success":
                            openSuccessNotificationWithIcon(r.data.message);
                            fetchTime();
                            break;
                        case "Error":
                            openNotificationWithIcon(r.data.message);
                            break;
                        default:
                            break;
                    }
                }
            })
            .then(() => queryClient.invalidateQueries("agentStatusInQueue"))
            .catch((e) => console.log(e));
    };

    /**
     * Unpause agent
     */
    const readyAgent = () => {
        // Ready agent
        setIsReady(true);
        setReadyButtonDisable(true);
        unPauseMutation.mutate({ reason: notReadyReason });
    };

    /**
     * Pause agent
     */
    const notReadyAgent = () => {
        setNotReadyVisible(true);
    };

    const submitNotReady = () => {
        notReadyMutation.mutate({ reason: notReadyReason });
        setIsLoading(true);
        // Not Ready agent
        /*notReadyMutation.mutate   ({ reason: notReadyReason }).then(r => {
                switch (r.data.response) {
                    case "Success":
                        openSuccessNotificationWithIcon(r.data.message)
                        break
                    case "Error":
                        openNotificationWithIcon(r.data.message)
                        break
                    default:
                        break
                }
            }).then(() => queryClient.invalidateQueries('isReadyQuery')).catch(e => console.log(e))*/
    };

    const submitWorkcode = (workcode) => {
        workcodeMutation.mutate({ code: workcode, channel: channelId });
    };

    const formProps = {
        formVisible,
        setFormVisible,
        isCallAccepted,
        setIsCallAccepted,
        number,
        selectedFormfromMenu,
        setOutgoingDialed
    };

    const workcodeProps = {
        workcodeVisible,
        setWorkcodeVisible,
    };

    const callReminderProps = {
        callReminder,
        setCallReminder,
        setCallReportForm,
        uniqueId: outCallState.outgoingCallId
    };

    const callReportFormProps = {
        callReportForm,
        setCallReportForm,
        setShowNotification,
    };

    const smsProps = {
        number,
        setSmsVisible,
        smsVisible
    };

    const campaignProps = {
        showCampaign,
        setShowCampaign,
        activeCampaign,
        setActiveCampaign,
        user: props.user,
    };

    const campaignPropsForSip = {
        campaignStatus,
        activeCampaign,
    };

    useEffect(() => {
        if (!isReadyQuery.data && isReadyQuery.data != null) setOnBreak(true);
        else setOnBreak(false);
    }, [isReadyQuery.data]);

    const fetchTime = async () => {
        try {
            const result = await apiClient.post("/api/agent/getTime");
            if (result.data) {
                setTimer(result.data.loginTime);
                setBreakTimer(result.data.breakTime);
            }
        } catch (error) {
            console.log("err", error);
        }
    };

    useLayoutEffect(() => {
        fetchTime();
    }, []);

    const handleLogout = () => {
        setLoading(true)
        // Fixed logout issue for expired sessions (BUG)
        apiClient.post(logout)
            .then(_ => {
                setLoading(false)
                setLoggedin(false)
                props.setIsLoggedIn(false)
                setUsername(null)
                sessionStorage.clear()
                localStorage.clear()
                history.push('/')

            })
            .catch(e => {
                setLoading(false)
                if (e.response) {
                    if (e.response.status === 401) {
                        sessionStorage.clear()
                        window.location.reload()

                    }
                }
            })
    }
    useEffect(() => {
        if (error) {
            openNotificationWithIcon('error', error)
        }
    }, [error])

    const handlePasswordModal = () => {
        setPasswordModal(!passwordModal)
    }

    const onChangePassword = (values) => {
        apiClient.post('/api/changePassword', values).then((res) => {
            openNotificationWithIcon('success', res.data)
            form.resetFields()
            setPasswordModal(!passwordModal)
            handleLogout();
        }).catch(err => {

            openNotificationWithIcon('error', err.response?.data?.message)

            form.resetFields()
            setPasswordModal(!passwordModal)
        })
    }

    const menu = (
        <Menu>
            <Menu.Item key={1} onClick={handlePasswordModal}>Change Password</Menu.Item>
            <Menu.Item key={2} onClick={handleLogout}>Logout</Menu.Item>
        </Menu>
    );


    // const onPaginationChange = (page, pageSize) => {
    //     setPaginate({ ...paginate, pageSize: pageSize, current: page });
    // };

    const onPaginationChange = (page, pageSize) => {
        setPaginate(prev => ({ ...prev, current: page, pageSize: pageSize }));
    };

    const columns = [
        {
            title: 'Source',
            dataIndex: 'src',
            key: 'src',
            // width: '200px'
        },
        {
            title: 'Date',
            dataIndex: 'date',
            key: 'date',
            // width: '200px',
            // render: (text) => <a>{text}</a>,
        },
        {
            title: 'Time',
            dataIndex: 'time',
            key: 'time',

        },
        {
            title: 'Destination',
            dataIndex: 'dst',
            key: 'dst',
        },
        {
            title: 'Wait Time',
            dataIndex: 'waittime',
            key: 'waittime',
        },
        {
            title: 'Action',
            key: 'action',
            render: (_, record) => (
                <Space size="middle">
                    <Button onClick={() => { handleStatusUpdate(record) }} style={{ fontSize: '14px' }} icon={<EditOutlined color='#000' />}>Update Status</Button>
                </Space>
            ),
        },
    ];

    const columnsCallBackRequest = [
        {
            title: 'Caller Number',
            dataIndex: 'caller_id',
            key: 'caller_id'
        },
        {
            key: "voiceMail",
            dataIndex: "voiceMail",
            title: "Voicemail/Callback",
            render: (text, record) => (
                record.fileLoc ? <audio controls src={`${streamURL}/${record.id}`} /> : "Call Back"
            ),
        },
        {
            title: 'Date',
            dataIndex: 'date',
            key: 'date'
        },
        {
            title: 'Time',
            dataIndex: 'time',
            key: 'time'
        },
        {
            title: 'Action',
            key: 'action',
            render: (_, record) => (
                <Space size="middle">
                    <Button onClick={() => { updateCallbackRequestStatus(record) }} style={{ fontSize: '14px' }} icon={<EditOutlined color='#000' />}>Update Status</Button>
                </Space>
            ),
        }
    ];

    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Layout.Header style={{ background: '#fff' }}>
                <Row justify="space-between">
                    <Col>
                        <Image preview={false} src={logo} height={60} alt="Contact+ Logo" />
                    </Col>
                    <Col>
                        <Space>
                            <Button
                                shape="circle"
                                title="Help"
                                icon={<QuestionCircleOutlined />}
                                style={{ border: 'none' }}
                                href="https://manuals.telecard.com.pk/agent"
                                target="_blank"
                            />
                            {username && <Dropdown trigger={['click']} overlay={menu}>
                                <Button
                                    icon={<UserSwitchOutlined />}
                                    size="large"
                                    style={{
                                        border: 'none',
                                        boxShadow: 'none',
                                    }}
                                >
                                    {username || ''}
                                </Button>
                            </Dropdown>}
                        </Space>
                    </Col>
                </Row>
            </Layout.Header>
            <Layout>
                <Layout.Sider theme="light" collapsed>
                    <Menu
                        defaultSelectedKeys={["1"]}
                        mode="inline"
                        theme="light"
                        inlineCollapsed={true}
                        style={{ minHeight: "79vh" }}
                    >
                        {props.user?.type !== 'Inbound' && (
                            <Menu.Item
                                onClick={showDialerDrawer}
                                key="1"
                                icon={<ContainerOutlined />}
                            >
                                Dialer
                            </Menu.Item>
                        )}
                        <Menu.Item
                            onClick={showDialerAccount}
                            key="2"
                            icon={<SettingOutlined />}
                        >
                            Settings
                        </Menu.Item>
                        <Menu.Item onClick={registerSIP} key="3" icon={<CodeOutlined />}>
                            Register
                        </Menu.Item>
                        <Menu.Item onClick={unregisterSIP} key="4" icon={<StopOutlined />}>
                            Unregister
                        </Menu.Item>
                        <Menu.Item onClick={loginQueue} key="5" icon={<ClusterOutlined />}>
                            Queue Login
                        </Menu.Item>
                        <Menu.Item
                            onClick={logoutQueue}
                            key="6"
                            icon={<DeliveredProcedureOutlined />}
                        >
                            Queue Logout
                        </Menu.Item>
                        <Menu.Item
                            // disabled={readyButtonDisable || notReadyStatus}
                            // disabled={notReadyStatus}
                            disabled={isReadyQuery.data}
                            onClick={readyAgent}
                            key="7"
                            icon={
                                <Spin spinning={isReady}>
                                    <CheckCircleOutlined />
                                </Spin>
                                // <CheckCircleOutlined />
                            }
                        >
                            Ready
                        </Menu.Item>
                        <Menu.Item
                            // disabled={
                            //     (isReadyQuery.data?.length > 0
                            //         ? !isReadyQuery.data[0]
                            //         : !isReadyQuery.data)}
                            disabled={!isReadyQuery.data}
                            onClick={notReadyAgent}
                            key="8"
                            icon={<CloseCircleOutlined />}
                        >
                            Not Ready
                        </Menu.Item>
                        <Menu.Item
                            danger={incomingCallAccepted || outgoingCallAccepted}
                            onClick={() => setWorkcodeVisible(true)}
                            key="10"
                            icon={<FormOutlined />}
                        >
                            Workcode
                        </Menu.Item>
                        <Menu.Item
                            danger={incomingCallAccepted || outgoingCallAccepted}
                            onClick={() => setCallReminder(true)}
                            icon={<PhoneOutlined />}
                        >
                            Schedule Call
                        </Menu.Item>
                        {/* <Menu.Item
                            danger={incomingCallAccepted || outgoingCallAccepted}
                            onClick={() => setCallReportForm(true)}
                            icon={<FileTextOutlined />}
                        >
                            Call Remark
                        </Menu.Item> */}
                        {/* <Menu.Item
                            danger={incomingCallAccepted || outgoingCallAccepted}
                            onClick={() => setSmsVisible(true)}
                            key="sms"
                            icon={<MessageOutlined />}
                        >
                            SMS
                        </Menu.Item> */}

                        {props.user?.sms_module !== 'no' && (
                            <Menu.Item
                                danger={incomingCallAccepted || outgoingCallAccepted}
                                onClick={() => setSmsVisible(true)}
                                key="sms"
                                icon={<MessageOutlined />}
                            >
                                SMS
                            </Menu.Item>
                        )}
                        {props.user?.email_module !== 'no' && (
                            <Menu.Item
                                danger={incomingCallAccepted || outgoingCallAccepted}
                                onClick={openModal} // Modal open karne ka function
                                key="email"
                                icon={<MailOutlined />}
                            >
                                Email
                            </Menu.Item>
                        )}

                        {showModal && <MyModal closeModal={closeModal} />}
                        {forms.length > 0 &&
                            forms.map((value, index) => (
                                <Menu.Item
                                    // danger={incomingCallAccepted || outgoingCallAccepted}
                                    onClick={(e) => {
                                        setFormVisible(true);
                                        number
                                            ? apiClient
                                                .get(
                                                    `api/getFormData?phone_number=${number}&form_id=${value.id}`
                                                )
                                                .then((res) => {
                                                    setSelectedFormfromMenu(res.data);
                                                })
                                            : apiClient
                                                .get(`api/getFormData?form_id=${value.id}`)
                                                .then((res) => {
                                                    setSelectedFormfromMenu(res.data);
                                                });
                                    }}
                                    key={11 + index}
                                    icon={<SnippetsOutlined />}
                                >
                                    {value.name}
                                </Menu.Item>
                            ))}
                    </Menu>
                </Layout.Sider>
                <Layout.Content style={{ padding: '24px' }}>
                    <SIPModule
                        ref={(elem) => (sipModule = elem)}
                        name={props.name}
                        sipDomain={props.sipDomain}
                        authUser={props.authUser}
                        authPass={props.authPass}
                        wssPort={props.wssPort}
                        dialerVisible={dialerVisible}
                        setDialerVisible={setDialerVisible}
                        onClose={onDialerClose}
                        onDialerAccountClose={onDialerAccountClose}
                        dialerAccountVisible={dialerAccountVisible}
                        queues={props.queues}
                        setConnected={setConnected}
                        setCallHangup={setCallHangup}
                        {...campaignPropsForSip}
                        {...incomingCallProps}
                        {...outgoingCallProps}
                        setNumber={setNumber}
                        settings={props.settings}
                        user={props.user}
                        callId={callState.callId}
                        outgoindCallId={outCallState.outgoingCallId}
                        time={timer}
                        break={breakTimer}
                        onBreak={onBreak}
                        queueLoggedIn={queueLoggedIn}
                        isCallAccepted={isCallAccepted}
                        setIsCallAccepted={setIsCallAccepted}
                        setOutgoingDialed={setOutgoingDialed}
                        setFormVisible={setFormVisible}
                        setCallReminder={setCallReminder}
                        setCallReportForm={setCallReportForm}

                        number={number}
                        redialClick={redialClick}
                        setRedialClick={setRedialClick}
                        redialId={redialId}
                        setRedialId={setRedialId}
                        showNotification={showNotification}
                        setShowNotification={setShowNotification}
                    //   formWidgetDataOnCallAcceptance={formWidgetDataOnCallAcceptance}
                    //   setFormWidgetDataOnCallAcceptance={setFormWidgetDataOnCallAcceptance}
                    />
                    <CampaignWidget {...campaignProps} />
                    <FormWidget {...formProps} />
                    <WorkcodeWidget {...workcodeProps} />
                    <CallReportForm {...callReportFormProps} />
                    <CallReminder {...callReminderProps} />
                    <SMSModule {...smsProps} />
                    {/* <MailBoxComponents {...openMailBox} /> */}
                    <AgentStatusWidget
                        isLogin={queueLoggedIn}
                        isReady={isReadyQuery.data}
                        reason={aStatsQuery.data?.Pausedreason}
                    />
                    {/*<Workcode submitWorkcode={submitWorkcode} channelId={channelId} setCallHangup={setCallHangup} callHangup={callHangup} data={workcodeQuery.data} isLoading={workcodeMutation.isLoading} />*/}
                    <NotReady
                        setNotReadyReason={setNotReadyReason}
                        onOk={submitNotReady}
                        isLoading={isLoading}
                        onCancel={() => setNotReadyVisible(false)}
                        visible={notReadyVisible}
                        data={pauseReasonQuery.data}
                    //   readyButtonDisable={readyButtonDisable}
                    //   setReadyButtonDisable={setReadyButtonDisable}
                    />
                    <DialerAccount
                        onClose={onDialerAccountClose}
                        visible={dialerAccountVisible}
                        {...settingsProps}
                    />
                    <Row>
                        {/* <Col span={8}>
                            <Spin spinning={scriptState.isLoading}>
                                <WidgetScript
                                    reload={() => dispatch(fetchAgentScript(queueState.queues, userId))}
                                    data={scriptState.data[0]}
                                    title="Script"
                                />
                            </Spin>
                        </Col> */}

                        <Col span={8}>
                            {/* {Array.isArray(scriptState.data) && scriptState.data.length > 0 ? (
                                <Spin spinning={scriptState.isLoading}>
                                    <WidgetScript
                                        reload={() => dispatch(fetchAgentScript(queueState.queues, userId))}
                                        data={scriptState.data[0]}
                                        title="Script"
                                    />
                                </Spin>
                            ) : (
                                <WidgetScript
                                    reload={null} // No reload
                                    data={{ content: 'No script assigned to this queue.' }}
                                    title="Script"
                                />
                            )} */}

                            {Array.isArray(queueScript) && queueScript.length > 0 ? (
                                <WidgetScript
                                    reload={fetchQueueScript} // manual reload always allowed
                                    data={queueScript[0]}
                                    title="Script"
                                />
                            ) : (
                                <WidgetScript
                                    reload={fetchQueueScript} // allow retry even when no script
                                    data={{ content: 'No script assigned to this queue.' }}
                                    title="Script"
                                />
                            )}
                        </Col>

                        {queueStats && (
                            <Col span={8}>
                                <Spin spinning={qStatsQuery.isLoading}>
                                    <Widget
                                        reload={refreshQStats}
                                        data={qStatsQuery.data}
                                        title="Queue-stats"
                                    />
                                </Spin>
                            </Col>
                        )}
                        {agentStats && (
                            <Col span={8}>
                                <Spin spinning={aStatsQuery.isLoading}>
                                    <Widget
                                        reload={refreshAStats}
                                        data={aStatsQuery.data}
                                        title="Agent-stats"
                                    />
                                </Spin>
                            </Col>
                        )}
                        {cdrStats && (
                            <Col span={24}>
                                <CallDetailWidget
                                    loading={aCDRQuery.isLoading}
                                    data={aCDRQuery.data}
                                    setCdrPage={setCdrPage}
                                    setFilteredNumber={setFilteredNumber}
                                    filteredNumber={filteredNumber}
                                />
                            </Col>
                        )}
                        {lastCallStats && (
                            <Col span={24}>
                                <Last5CallHistory loading={false} data={callHistory} />
                            </Col>
                        )}


                    </Row>
                    <br />

                    <Card title={"Abandoned Calls"}>
                        <Table
                            key={(abandoncall, index) => index}
                            columns={columns}
                            dataSource={abandoncall?.data?.data}
                            pagination={{
                                current: paginate.abandonCalls.current,
                                pageSize: paginate.abandonCalls.pageSize,
                                total: paginate.abandonCalls.total,
                                showSizeChanger: true,
                                // onChange: onPaginationChange,
                                onChange: handlePaginationChange('abandonCalls'),
                            }}
                            loading={abandoncall.isLoading}
                        />
                    </Card>


                    <ScheduleCallBackTable
                        setNumber={setNumber}
                        setRedialClick={setRedialClick}
                        setDialerVisible={setDialerVisible}
                        setRedialId={setRedialId}
                    />

                    <br />
                    {
                        sessionStorage.getItem('view_callback') > 0 ?
                            (
                                <Card title={"Callback Request"}>
                                    <Table
                                        columns={columnsCallBackRequest}
                                        dataSource={callbackRequest?.data?.data}
                                        pagination={{
                                            current: paginate.callbackRequests.current,
                                            pageSize: paginate.callbackRequests.pageSize,
                                            total: paginate.callbackRequests.total,
                                            showSizeChanger: true,
                                            onChange: handlePaginationChange('callbackRequests'),
                                        }}
                                        loading={callbackRequest.isLoading}
                                    />
                                </Card>) : ""
                    }

                    {/* <Pagination
                        showSizeChanger
                        // showQuickJumper
                        // showTotal={(total, range) => {
                        //     return `${range[0]}-${range[1]} of ${total} items`;
                        // }}
                        onChange={onPaginationChange}
                        defaultCurrent={paginate.current}
                        pageSize={paginate.pageSize}
                        total={paginate.total}
                        style={{
                            display: "flex",
                            justifyContent: "end",
                            alignItems: "end",
                            marginTop: "1rem",
                        }}
                    /> */}

                    <ChangePasswordModal
                        isModalOpen={passwordModal}
                        handleOk={onChangePassword}
                        form={form}
                        onCancel={() => {
                            setPasswordModal(!passwordModal)
                            form.resetFields()
                        }}
                    />
                </Layout.Content>
            </Layout>
            <Layout.Footer
                style={{ background: '#fff', textAlign: 'center', fontWeight: 'bold', color: '#15347c' }}
            >
                ContactPlus ©{new Date().getFullYear()} | Powered by Telecard Ltd.
            </Layout.Footer>
        </Layout>
    );
}


const ChangePasswordModal = ({ isModalOpen, handleOk, onCancel, form }) => {
    return (
        <Modal
            title="Change Password"
            visible={isModalOpen}
            onOk={() => {
                form.validateFields().then(values => {
                    handleOk(values)
                })
            }}
            onCancel={onCancel}
        >
            <Form
                size="large"
                form={form}
            >
                <Form.Item
                    name="current_password"
                    rules={[
                        {
                            required: true,
                            message: 'Please input your password!',
                        },
                    ]}
                >
                    <Input.Password placeholder="Enter Current Password." />
                </Form.Item>

                <Form.Item
                    name="new_password"
                    rules={[
                        {
                            required: true,
                            message: 'Please input your password!',
                        },
                    ]}
                    hasFeedback
                >
                    <Input.Password placeholder="Enter New Password." />
                </Form.Item>

                <Form.Item
                    name="new_confirm_password"
                    dependencies={['new_password']}
                    hasFeedback
                    rules={[
                        {
                            required: true,
                            message: 'Please confirm your password!',
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('new_password') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error('The two passwords that you entered do not match!'));
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="Enter Confirm Password." />
                </Form.Item>
            </Form>
        </Modal>
    )
}
