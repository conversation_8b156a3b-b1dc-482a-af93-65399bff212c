import { Button, Form, Modal, Select } from "antd";

const NotReady = props => {
    const [form] = Form.useForm();

    return (
        <Modal
            title="Specify Reason"
            centered={true}
            keyboard={false}
            maskClosable={false}
            okText="Submit"
            visible={props.visible}
            onOk={props.onOk}
            onCancel={props.onCancel}
            footer={[
                <Button
                    onClick={props.onCancel}
                >
                    Cancel
                </Button>,
                <Button key="submit" type="primary" loading={props.isLoading} onClick={() => form.validateFields().then(() => props.onOk())}>
                    Submit
                </Button>
            ]}
        >
            <Form form={form}>
                <Form.Item name={'reason'} rules={[
                    {
                        required: true,
                        message: "Please enter reason"
                    }
                ]}>
                    <Select placeholder="Select Reason" onChange={value => props.setNotReadyReason(value)} style={{ width: '100%' }} size="large">
                        {props.data && props.data.map((value, index) => <Select.Option key={value.id} value={value.name}>{value.name}</Select.Option>)}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    )
}

export default NotReady