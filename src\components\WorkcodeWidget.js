import { Form, Input, Modal, Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { getWorkcodes, postWorkcode } from "../Actions/WorkcodeAction";
import { SaveOutlined } from "@ant-design/icons";
import openNotificationWithIcon from "./Notification";
import openSuccessNotificationWithIcon from "./Message";

export const WorkcodeWidget = ({ workcodeVisible, setWorkcodeVisible }) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const workcodeState = useSelector((state) => state.WorkcodeReducer);
    const { callId } = useSelector((state) => state.CallReducer);
    const { outgoingCallId } = useSelector((state) => state.OutgoingCallReducer);
    const { queues } = useSelector((state) => state.QueueReducer);
    const userId = sessionStorage.getItem('id');

    // console.log("workcode State", workcodeState);
// old bkp
    // useEffect(() => {
    //     if (!workcodeState.isLoading && workcodeState.errMess) {
    //         openNotificationWithIcon(workcodeState.errMess);
    //     } else if (!workcodeState.isLoading && workcodeState.message) {
    //         openSuccessNotificationWithIcon(workcodeState.message);
    //     }
    // }, [workcodeState]);
//added for repeated notification
    useEffect(() => {
        if (!workcodeState.isLoading && workcodeState.errMess) {
            openNotificationWithIcon("error",workcodeState.errMess);
            dispatch({ type: "RESET_WORKCODE_MESSAGE" }); 
        } else if (!workcodeState.isLoading && workcodeState.message) {
            openSuccessNotificationWithIcon(workcodeState.message);
            dispatch({ type: "RESET_WORKCODE_MESSAGE" }); 
        }
    }, [workcodeState, dispatch]);


    // useEffect(() => {
    //     dispatch(getWorkcodes());
    // }, []);

    useEffect(() => {
        dispatch(getWorkcodes(userId));
    }, [userId]);

    // useEffect(() => {
    //     if (workcodeState !== null) {
    //         console.log("Socket WorkCode Query Final:", workcodeState);
    //     }
    // }, [workcodeState]);

    useEffect(() => {
        if (form && workcodeVisible) {
            form.setFieldsValue({
                channel: callId || outgoingCallId,
                queue: queues[0],
            });
        }
    }, [callId, queues, outgoingCallId, workcodeVisible, form]);

    return (
        <Modal
            visible={workcodeVisible}
            onCancel={() => setWorkcodeVisible(false)}
            okText="Submit"
            title="Submit Workcode"
            onOk={() =>
                form
                    .validateFields()
                    .then((values) => dispatch(postWorkcode(values)))
                    .then(() => form.resetFields(["queue", "code"]))
                    .then(() => setWorkcodeVisible(false))
                    .catch((e) => console.log(e))
            }
            okButtonProps={{
                loading: workcodeState.isLoading,
                icon: <SaveOutlined />,
            }}
        >
            <Form layout={"vertical"} form={form}>
                <Form.Item
                    label="Call ID"
                    name="channel"
                    rules={[
                        {
                            required: true,
                            message: 'Call Id is required!',
                        }
                    ]}
                >
                    <Input disabled />
                </Form.Item>
                <Form.Item
                    label="Queue"
                    name="queue"
                    rules={[
                        {
                            required: true,
                            message: 'Queue is required!',
                        }
                    ]}
                >
                    <Select>
                        {queues &&
                            queues.map((v, i) => (
                                <Select.Option key={i} value={v}>
                                    {v}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>
                <Form.Item
                    label="Workcode"
                    name="code"
                    rules={[
                        {
                            required: true,
                            message: 'WorkCode is required!',
                        }
                    ]}
                >
                    <Select showSearch optionFilterProp="children">
                        {workcodeState.workcodes &&
                            workcodeState.workcodes.map((value, index) => (
                                <Select.Option key={index} value={value.id}>
                                    {value.name}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );
};
