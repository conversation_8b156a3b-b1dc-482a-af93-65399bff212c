import { Button, Result } from "antd";
import React from "react";
import openNotificationWithIcon from "../components/Notification";

export default class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI.
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // You can also log the error to an error reporting service
        openNotificationWithIcon(error.message)
    }

    render() {
        if (this.state.hasError) {
            // You can render any custom fallback UI
            return (
                <Result
                    status="500"
                    title="500"
                    subTitle="Sorry, something went wrong."
                    // extra={<Button type="primary">Back Home</Button>}
                />
            );
        }

        return this.props.children;
    }
}