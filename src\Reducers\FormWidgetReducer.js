import * as ActionTypes from "../Constants/FormWidgetConstant"
const initialState = {
    isLoading: false,
    forms: [],
    errMess: false
}

export const FormWidgetReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.FORM_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.FORM_SUCCESS:
            return {...state, forms: action.payload, isLoading: false}
        case ActionTypes.FORM_FAILED:
            return {...state, errMess: action.payload, isLoading: false}
    }
}