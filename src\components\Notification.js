// import {notification} from "antd";

// const openNotificationWithIcon = (message) => {
//     notification['error']({
//         message: 'Error',
//         description: message,
//     });
// }

// export default openNotificationWithIcon

import { notification } from "antd";

const openNotificationWithIcon = (type, content) => {
    const validTypes = ['success', 'error', 'info', 'warning'];

    if (!validTypes.includes(type)) {
        console.error(`Invalid notification type: ${type}`);
        return;
    }

    notification[type]({
        message: type === 'error' ? 'Error' : 'Success',
        description: content,
    });
}

export default openNotificationWithIcon;
