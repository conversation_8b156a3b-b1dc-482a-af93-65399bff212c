import React, { useState, useEffect } from "react";
import Chatbot from "./Chatbot"; // Import Chatbot component
import { IoChatbubbleEllipsesOutline } from "react-icons/io5"; // Import chat icon
import Echo from "laravel-echo";
import Pusher from "pusher-js";
import apiClient from "../config/apiClient";


const ChatIcon = () => {
  const [userId, setUserId] = useState(null); // Authenticated user ID
  // const [unreadCount, setUnreadCount] = useState(0);
  const [isChatOpen, setIsChatOpen] = useState(false);
  // const authToken = sessionStorage.getItem("auth_token");
  const [unreadCount, setUnreadCount] = useState(0)

  // Toggle the chat window visibility

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  const fetchAuthUser = async () => {
    try {
        const response = await apiClient.get("/api/get-user");
        // console.log("Authenticated User:", response.data);
        setUserId(response.data.id); 
    } catch (error) {
        console.error("Error fetching user:", error.response?.data || error.message);
        }
    };
    useEffect(() => {
        fetchAuthUser();
    }, []);

    const fetchUnreadCounts = async () => {
      try {
        const { data } = await apiClient.get("/api/conversation/messages/unread-count");
        // console.log("API Response:", data);
  
        setUnreadCount(data.total_unread || 0); 
      } catch (error) {
        console.error("API Error:", error);
      }
    };

    // console.log("Pusher Key:", process.env.REACT_APP_PUSHER_KEY);
    // console.log("Cluster:", process.env.REACT_APP_PUSHER_CLUSTER);
    // console.log("WebSocket Host:", process.env.REACT_APP_SOCKET_APP_URL);
    // console.log("WebSocket Port:", process.env.REACT_APP_SOCKET_PORT);
    // console.log("Force TLS:", process.env.REACT_APP_PUSHER_FORCETLS);
    // console.log("Disable Stats:", process.env.REACT_APP_PUSHER_DISABLE_STATS);
    // console.log("Encrypted:", process.env.REACT_APP_PUSHER_ENCRYPTED);
    
    useEffect(() => {
      if (!userId) return;
    
      fetchUnreadCounts(); 
     
      window.Pusher = Pusher;
      const echo = new Echo({
        broadcaster: "pusher",
        // cluster: "ap2",
        // key: "cc92dcbb81323d642b63",
        // wsHost: "127.0.0.1",
        // wsPort: 6001,
        // forceTLS: false,
        // disableStats: true,

        key: process.env.REACT_APP_PUSHER_KEY ?? 'local',
        cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? 'mt1',
        wsHost: process.env.REACT_APP_SOCKET_APP_URL ?? 'solutionsv3.tclcontactplus.com',
        wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
        forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true, // Disable TLS for local development
        disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
        encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
      });
    
    //   console.log(`Subscribing to unread count channel: unread_message_count.${userId}`);
    
      
      const unreadChannel = echo.channel(`unread_message_count.${userId}`);
    
      unreadChannel.listen("UnreadMessageCountUpdated", () => {
        // console.log("Unread count update event received, refetching...");
        fetchUnreadCounts();
      });
    
      return () => {
        // console.log(`Leaving unread count channel: unread_message_count.${userId}`);
        echo.leave(`unread_message_count.${userId}`);
      };
    }, [userId]);
    
    

  // const toggleChat = () => {
  //   setIsChatOpen((prev) => !prev);

  //   if (!isChatOpen) {
  //     markAsRead(); //
  //   }
  // };

  return (
    <div>
      {/* Floating chat icon with animation */}
      <div
        onClick={toggleChat}
        style={chatIconStyle}
        title="Chat with us"
      >
        <IoChatbubbleEllipsesOutline size={30} />
        {unreadCount > 0 && <span style={unreadCountStyle}>{unreadCount}</span>}
        </div>

      {/* Show the chatbot when it's open */}
      {isChatOpen && <Chatbot fetchUnreadCounts={fetchUnreadCounts} onClose={toggleChat} />}
    </div>
  );
};

const chatIconStyle = {
  position: "fixed",
  bottom: "25px",
  right: "25px",
  backgroundColor: "#4CAF50", 
  color: "white",
  borderRadius: "50%", 
  padding: "15px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "24px",
  cursor: "pointer",
  boxShadow: "0 4px 10px rgba(0, 0, 0, 0.3)", 
  transition: "all 0.3s ease", 
  zIndex: 1000, 
};

const unreadCountStyle = {
  position: "absolute",
  top: "-5px",
  right: "-5px",
  background: "linear-gradient(135deg, #ff4d4d, #ff0000)",  
  color: "#fff",
  fontSize: "12px",
  fontWeight: "bold",
  padding: "4px 7px",
  borderRadius: "50%",
  minWidth: "20px",
  minHeight: "20px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};




export default ChatIcon;
