import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from "react"
import { useQuery } from "react-query";
import { fetchDomain, fetchUser } from "../config/queries";
import { <PERSON><PERSON>, Card, Col, Result, Row, Spin, Tag, Typography } from "antd";
import { CodeOutlined, CodeSandboxOutlined, LogoutOutlined, PhoneOutlined, UserOutlined } from "@ant-design/icons";
import SIPConfig from "../classes/SIPConfig";
import initializeEcho from '../config/echo';

const DialerConfig = forwardRef((props, ref) => {

    const userId = sessionStorage.getItem('id');
    const echo = initializeEcho(sessionStorage.getItem('agent_token'));


    useImperativeHandle(ref, () => ({
        registerPhone: () => sipClientRef.current.RegisterSIP(),
        isRegistered: () => sipClientRef.current.isRegistered(),
        unregisterPhone: () => sipClientRef.current.UnregisterSIP(),
        makeCall: number => sipClientRef.current.onMakeCall(number),
    }))

    // const domainQuery = useQuery('fetchDomain', fetchDomain)
    const [domainQuery, setDomainQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-systemsetting-channel.${userId}`);

        setDomainQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));
        
        channel.listen(".agent-panel-systemsetting", (e) => {
            setDomainQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data[0],
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-systemsetting");
        };
    }, [userId]);


    // console.log("domain data new 01c: ", domainQuery?.data)

    // const userQuery = useQuery('fetchUser', fetchUser)
    const [userQuery, setUserQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-getuser-channel.${userId}`);

        setUserQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));
        
        channel.listen(".agent-panel-getuser", (e) => {
            setUserQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data,
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-getuser");
        };
    }, [userId]);

    const sipClientRef = useRef()

    const registerPhone = () => {
        sipClientRef.current.RegisterSIP()
    }

    const unregisterPhone = () => {
        sipClientRef.current.UnregisterSIP()
    }

    if (userQuery.isSuccess && userQuery.data.type === "Normal") {
        return (
            <Result
                status="error"
                title="401"
                subTitle="Sorry, you are unauthorized to login into this application. Please contact your system administrator."
                extra={<Button icon={<LogoutOutlined />} type="primary">Logout</Button>}
            />
        )
    } else if (userQuery.isError || domainQuery.isError) {
        if (userQuery.isError) {
            return (
                <Result
                    status={userQuery.error.status}
                    title={userQuery.error.status}
                    subTitle={userQuery.error.statusText}
                    extra={<Button onClick={() => window.location.reload()} type="primary">Refresh Page</Button>}
                />
            )
        } else {
            return (
                <Result
                    status={domainQuery.error.status}
                    title={domainQuery.error.status}
                    subTitle={domainQuery.error.statusText}
                    extra={<Button onClick={() => window.location.reload()} type="primary">Refresh Page</Button>}
                />
            )
        }
    } else if (domainQuery.isLoading || userQuery.isLoading) {
        return (
            <div>Loading...</div>
        )
    } else return (
        <Spin spinning={domainQuery.isLoading || userQuery.isLoading}>
            <SIPConfig ref={sipClientRef} name={userQuery.data.name} sipDomain={domainQuery.data.server_address} authUser={userQuery.data.auth_username} authPass={userQuery.data.auth_password} wssPort={domainQuery.data.wss_port} />
        </Spin>
    )
})

export default DialerConfig