import apiClient from "./apiClient";
import {
    aCDRs,
    aStats,
    domain,
    getAgentStatusInQueue,
    getChannelId,
    getPauseReasons,
    getQueue,
    getUser,
    getWorkcodes,
    isReady,
    last5Calls,
    loginQueue,
    logoutQueue,
    notReadyAgent,
    prepaidBil,
    qStats,
    readyAgent,
    broadcastEvents,
} from "./routes";

export const fetchDomain = async () => {

    // return apiClient.get(domain).then((response) => response.data[0]);
    const response = await apiClient.get(domain);
    if (response) {
        // console.log("responsee", response.data)
        return response.data[0]
    }
};

export const fetchUser = () => {
    return apiClient.get(getUser).then((response) => response.data);
};

export const dispatchBroadcastEvents = () => {
    return apiClient.get(broadcastEvents).then((response) => response.data);
};

export const fetchQueue = () => {
    return apiClient.get(getQueue).then((res) => res.data);
};

export const fetchLoginQueue = async () => {
    const res = await apiClient.post(loginQueue);
    // console.log("login_time", res.data);
    sessionStorage.setItem("login_time", res.data[1]);
    return res.data;
};

// prepaid bil
export const fetchPrepaidBil = async () => {
    const res = await apiClient.get(prepaidBil).then(res => res.data);
}
// end prepaid 

export const fetchLogoutQueue = async () => {
    const res = await apiClient.post(logoutQueue);
    sessionStorage.removeItem("login_time");
    return res.data;
};

export const fetchReadyQueue = async () => {
    const res = await apiClient.post(readyAgent);
    res.data && sessionStorage.removeItem("break_time");
    return res.data;
};

export const fetchNotReadyQueue = async () => {
    let res = await apiClient.post(notReadyAgent);
    return res.data;
};

export const fetchIsReady = () => {
    return apiClient.post(isReady).then((res) => res.data);
};

export const fetchQStats = () => {
    return apiClient.post(qStats).then((res) => res.data);
};

export const fetchAStats = () => {
    return apiClient.post(aStats).then((res) => res.data);
};

export const fetchACDRs = (query) => {
    // console.log("object number", query.queryKey?.[2]);
    // console.log("object number2", query.queryKey?.[1]);
    return query.queryKey?.[2]
        ? apiClient
            .post(`${aCDRs}?page=${query.queryKey?.[1] || 1}`, {
                number: query.queryKey?.[2],
            })
            .then((res) => [res.data])
        : apiClient
            .post(`${aCDRs}?page=${query.queryKey?.[1] || 1}`)
            .then((res) => [res.data]);

};

export const fetchLast5Calls = (number) => {
    return apiClient.post(last5Calls, { src: number });
};

export const fetchWorkcodes = () => {
    return apiClient.get(getWorkcodes).then((res) => res.data);
};

export const fetchChannelId = () => {
    return apiClient.post(getChannelId).then((res) => res.data);
};

export const fetchPauseReasons = () => {
    return apiClient.get(getPauseReasons).then((res) => res.data);
};

export const fetchAgentStatusInQueue = () => {
    return apiClient.post(getAgentStatusInQueue).then((res) => res.data);
};

export const fetchUserCampaigns = (user) =>
    apiClient.get(`/api/${user}/campaign/user`).then((r) => r.data);

export const logStartCampaign = (data) =>
    apiClient.post(`/api/campaignLog`, data).then((r) => r.data);

export const fetchCampaignNumber = (campaign) =>
    apiClient.get(`/api/${campaign}/number`).then((r) => r.data);

export const updateStateOnServer = (data) =>
    apiClient
        .post(`/api/${data.campaign}/${data.campaignNumber}/update`)
        .then((r) => r.data);
