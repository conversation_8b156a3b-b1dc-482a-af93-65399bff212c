import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Descriptions, Drawer, Space, Table, Tag } from "antd";
import { useMutation, useQuery } from "react-query";
import { fetchLogoutQueue, fetchUserCampaigns, logStartCampaign } from "../config/queries";
import { CheckCircleOutlined, MinusCircleOutlined } from "@ant-design/icons";
import openNotificationWithIcon from "./Notification";
import openSuccessNotificationWithIcon from "./Message";
import initializeEcho from '../config/echo';

const CampaignWidget = (props) => {
    const [currentTime, setCurrentTime] = useState(new Date());
    const [disable, setDisable] = useState(false);

    const userId = sessionStorage.getItem('id');
    const echo = initializeEcho(sessionStorage.getItem('agent_token'));

    const handleError = (error) =>
        error.response ? error.response.data.message : error.message;

    const { activeCampaign, setActiveCampaign, user, campaignNumbersData } =
        props;

    // console.log("campaign number data", campaignNumbersData)

    // const query = useQuery(
    //     ["campaign", user.id],
    //     () => fetchUserCampaigns(user.id),
    //     {
    //         refetchInterval: 10000,
    //         refetchIntervalInBackground: true,
    //     }
    // );

    const [query, setQuery] = useState({});

    useEffect(() => {
        // Listen for WebSocket events
        const channel = echo.private(`agent-panel-user-campaign-channel.${user.id}`);

        channel.listen(".agent-panel-user-campaign", (e) => {
            // console.log("agent-panel-user-campaign : " + e.data);
            setQuery(e.data);
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".agent-panel-user-campaign");
        };
    }, [user]);


    // console.log("active campaign", activeCampaign)
    const { data } = query;
    // console.log("agent-panel-user-campaign Final Data: " + JSON.stringify(query));

    const mutation = useMutation(logStartCampaign, {
        onSuccess: (data1, variables) => {
            openSuccessNotificationWithIcon(data1);
            // console.log("variables", variables)
            switch (variables.event) {
                default:
                    break;
                case "AGENT_START":
                    setActiveCampaign(
                        query.data.find((d) => d.id === variables.campaign_id)
                    );
                    setDisable(variables.campaign_id);
                    break;
                case "AGENT_STOP":
                    setActiveCampaign(false);
                    setDisable(false);
                    break;
            }
        },
        onError: (error, variables) => openNotificationWithIcon(handleError(error)),
    });

    const handleStartCampaign = (campaignID) => {
        if (!activeCampaign) {
            mutation.mutate({
                user_id: user.id,
                campaign_id: campaignID,
                event: "AGENT_START",
            });
        } else {
            mutation.mutate({
                user_id: user.id,
                campaign_id: campaignID,
                event: "AGENT_STOP",
            });
        }
    };

    const columns = [
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "City",
            dataIndex: "city",
            key: "city",
        },
        {
            title: "Number",
            dataIndex: "number",
            key: "number",
        },
    ];

    useEffect(() => {
        const intervalId = setInterval(() => {
            setCurrentTime(new Date());
        }, 3000);

        return () => {
            clearInterval(intervalId);
        };
    }, []);

    const logoutQueueQuery = useQuery("logoutQueue", fetchLogoutQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });

    return (
        <Drawer
            title="Campaign"
            placement="right"
            closable={true}
            visible={props.showCampaign}
            key="right"
            onClose={() => props.setShowCampaign(false)}
        >
            {data &&
                data.map((value, index) => (
                    <Descriptions
                        key={index}
                        column={1}
                        size="small"
                        layout="vertical"
                        bordered
                    >
                        <Descriptions.Item label="Name">{value.name}</Descriptions.Item>
                        <Descriptions.Item label="Start Time">
                            {value.start_time}
                        </Descriptions.Item>
                        <Descriptions.Item label="End Time">
                            {value.end_time}
                        </Descriptions.Item>
                        <Descriptions.Item label="Status">
                            <Badge
                                status={value.status ? "success" : "error"}
                                text={value.status ? "Active" : "Inactive"}
                            />
                        </Descriptions.Item>
                        <Descriptions.Item label="Agent Status">
                            <Tag color={value?.id === disable ? "success" : "error"}>
                                {value?.id === disable ? "ON" : "OFF"}
                            </Tag>
                        </Descriptions.Item>
                        <Descriptions.Item label="Active numbers">
                            {value.count}
                        </Descriptions.Item>
                        <Descriptions>
                            <Space>
                                <Button
                                    disabled={
                                        currentTime > new Date(value.end_time) ||
                                        value.status !== 1
                                    }
                                    size="small"
                                    danger={value?.id === disable}
                                    onClick={() => {
                                        handleStartCampaign(value?.id)
                                        logoutQueueQuery.refetch();
                                    }}
                                    icon={
                                        value?.id !== disable ? (
                                            <CheckCircleOutlined />
                                        ) : (
                                            <MinusCircleOutlined />
                                        )
                                    }
                                    type="primary"
                                >
                                    {value?.id !== disable ? "Start" : "Stop"}
                                </Button>
                            </Space>
                        </Descriptions>
                    </Descriptions>
                ))}
            {campaignNumbersData && (
                <Table
                    scroll={{ x: true }}
                    dataSource={[campaignNumbersData]}
                    columns={columns}
                />
            )}
        </Drawer>
    );
};

export default CampaignWidget;
