import { useEffect, useState } from "react";
import {
    Button,
    Card,
    Form,
    InputNumber,
    Pagination,
    Space,
    Table,
} from "antd";
import { FilterOutlined } from "@ant-design/icons";
import Input from "rc-input";

const CallDetailWidget = (props) => {
    const { setCdrPage, data, setFilteredNumber, filteredNumber } = props;
    const [dataSource, setDataSource] = useState([]);
    const [error, setError] = useState();

    const [form] = Form.useForm();

    useEffect(() => {
        setDataSource(data?.[0]?.data);
        // console.log("data", data?.[0]?.data);
    }, [data]);

    const columns = [
        // {
        //     title: 'Recording',
        //     dataIndex: 'recordingfile',
        //     key: 'recordingfile',
        //     //render: file => file ? <audio preload="metadata" controls src={process.env.REACT_APP_baseURL + '/api/download/' + file} /> : ''
        // },
        {
            title: "Source",
            dataIndex: "src",
            key: "src",
        },
        {
            title: "Dest.",
            dataIndex: "dst",
            key: "dst",
        },
        {
            title: "CLID",
            dataIndex: "clid",
            key: "clid",
        },
        {
            title: "Channel",
            dataIndex: "channel",
            key: "channel",
        },
        {
            title: "Dest. Chan.",
            dataIndex: "dstchannel",
            key: "dstchannel",
        },
        {
            title: "Last App",
            dataIndex: "lastapp",
            key: "lastapp",
        },
        {
            title: "Start",
            dataIndex: "start",
            key: "start",
        },
        {
            title: "Answer",
            dataIndex: "answer",
            key: "answer",
        },
        {
            title: "End",
            dataIndex: "end",
            key: "end",
        },
        {
            title: "dur.",
            dataIndex: "duration",
            key: "duration",
        },
        {
            title: "Bill Sec.",
            dataIndex: "billsec",
            key: "billsec",
        },
        {
            title: "Disp.",
            dataIndex: "disposition",
            key: "disposition",
        },
    ];

    const validatePhone = (value) => {
        const phoneNumberRegex = /^\d{1,13}$/;
        // Regex to match 1 to 13 digits

        if (value && !phoneNumberRegex.test(value)) {
            setError("Please enter a valid phone number");
        } else {
            setError();
        }

        setFilteredNumber(value);
    };

    const onPageChange = (page) => {
        // console.log("pagee", page)
        setCdrPage(page)

    };

    return (
        <Card>
            <Table
                rowKey={(r) => `${r.channel}_${r.sequence}`}
                loading={props.loading}
                scroll={{ x: true }}
                dataSource={dataSource}
                columns={columns}
                pagination={false}
                title={() => (
                    <div>
                        <Input
                            value={filteredNumber}
                            placeholder="Filter By Number"
                            onChange={(e) => validatePhone(e.target.value)}
                            style={{
                                border: "1px solid gray",
                                borderRadius: "5px",
                                padding: "3px 8px",
                            }}
                        />
                        {error && <div style={{ color: "red" }}>{error}</div>}
                    </div>
                )}
            />
            {localStorage.getItem("cdr-stats") && (
                <Pagination
                    total={data?.[0]?.total ?? 0}
                    // total={data[0]?.total}
                    style={{
                        display: "flex",
                        justifyContent: "end",
                        alignItems: "end",
                        marginTop: "1rem",
                    }}
                    showSizeChanger={false}
                    defaultPageSize={5}
                    onChange={onPageChange}
                    showQuickJumper
                    showTotal={(total) => `Total ${total} items`}
                />
            )}
        </Card>
    );
};

export default CallDetailWidget;
