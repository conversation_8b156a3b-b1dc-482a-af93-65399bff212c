import Echo from "laravel-echo";
import Pusher from "pusher-js";

window.Pusher = Pusher;

let echoInstance = null;

const initializeEcho = (token) => {
    if (!echoInstance) {
        echoInstance = new Echo({
            broadcaster: 'pusher',
            key: process.env.REACT_APP_PUSHER_KEY ?? 'local',
            cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? 'mt1',
            wsHost: process.env.REACT_APP_SOCKET_APP_URL ?? '', //solutionsv3.tclcontactplus.com
            wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
            forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true, // Disable TLS for local development
            disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
            encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
            authEndpoint: process.env.REACT_APP_PUSHER_AUTH_ENDPOINT ?? '', //https://solutionsv3.tclcontactplus.com/backend/broadcasting/auth
            auth: {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
            enabledTransports: ['ws', 'wss'],
            enableClientEvents: true,
        });

        console.log("✅ Echo WebSocket initialized");
    }
    else {
        // console.log("⚠️ Echo WebSocket already initialized. Skipping...");
    }

    return echoInstance;
};

export default initializeEcho;

// const initializeEcho = (token) => {
//     return new Echo({
//         broadcaster: 'pusher',
//         key: process.env.REACT_APP_PUSHER_KEY ?? 'local',
//         cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? 'mt1',
//         wsHost: process.env.REACT_APP_SOCKET_APP_URL ?? 'solutionsv3.tclcontactplus.com',
//         wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
//         forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true, // Disable TLS for local development
//         disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
//         encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
//         authEndpoint: process.env.REACT_APP_PUSHER_AUTH_ENDPOINT ?? 'https://solutionsv3.tclcontactplus.com/backend/broadcasting/auth',
//         auth: {
//             headers: {
//                 Authorization: `Bearer ${token}`,
//             },
//         },
//         enabledTransports: ['ws', 'wss'],
//         enableClientEvents: true,
//     });
// };

// export default initializeEcho;