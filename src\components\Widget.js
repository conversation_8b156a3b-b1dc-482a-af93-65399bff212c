import { Button, Card, Descriptions, Typography } from "antd"
import { ReloadOutlined } from "@ant-design/icons";

const Widget = props => {

    const { data, title, reload } = props


    return (
        <Card title={title} style={{ marginTop: 10, minHeight: '200px' }} extra={<Button onClick={reload} title="Reload" icon={<ReloadOutlined />} />}>
            <Descriptions
                bordered
                layout="vertical"
            >
                {data && Object.keys(data).map((value, index) => (<Descriptions.Item key={index} label={value}>{data[value]}</Descriptions.Item>))}
            </Descriptions>
        </Card>
    )
}

export const WidgetScript = props => {

    const { data, title, reload } = props

    return (<Card title={title} style={{ marginTop: 10, minHeight: '200px' }} extra={<Button onClick={reload} title="Reload" icon={<ReloadOutlined />} />}>
        <div dangerouslySetInnerHTML={{ __html: data?.content }}></div>
    </Card>)
}


export default Widget