import { useState, useEffect } from "react";
import {
    Button,
    Checkbox,
    DatePicker,
    Divider,
    Form,
    Input,
    Modal,
    Radio,
    Select,
    Space,
    Spin,
    Typography,
} from "antd";
import moment from "moment-timezone";
import { useDispatch, useSelector } from "react-redux";
import { getQueues } from "../Actions/QueueAction";
import { getForms } from "../Actions/FormWidgetAction";
import { postFormData } from "../Actions/FormDataAction";
import { CloseOutlined, MinusCircleOutlined, PlusOutlined, SaveOutlined } from "@ant-design/icons";
import openNotificationWithIcon from "./Notification";
import openSuccessNotificationWithIcon from "./Message";

export const FormWidget = ({
    formVisible,
    setFormVisible,
    number,
    selectedFormfromMenu,
    setOutgoingDialed
}) => {
    const queueState = useSelector((state) => state.QueueReducer);
    const formState = useSelector((state) => state.FormWidgetReducer);
    //   const [forms, setForms] = useState([]);
    const [queue, setQueue] = useState(false);
    const dispatch = useDispatch();
    const [form] = Form.useForm();
    const userId = sessionStorage.getItem('id');

    // useEffect(() => {
    //     dispatch(getQueues());
    // }, []);

    useEffect(() => {
        dispatch(getQueues(userId));
    }, [userId]);

    // useEffect(() => {
    //     if (queueState !== null) {
    //         console.log("Sockect Get-Queue Data Final:", queueState);
    //     }
    // }, [queueState]);


    useEffect(() => setQueue(queueState.queues[0]), [queueState]);
    useEffect(() => {
        if (queue) dispatch(getForms(queue));
    }, [queue]);

    const DynamicFormFunction = ({ label, value }) => {
        return (
            <div>
                <Typography.Text strong>{label} : </Typography.Text>
                <Typography.Text>{value}</Typography.Text>
            </div>
        );
    };

    const formFunction = selectedFormfromMenu?.fields?.form_functions.map((field) => (
        <DynamicFormFunction
            key={field.id}
            label={field.label}
            value={selectedFormfromMenu?.values && selectedFormfromMenu?.values[field.name]}
        />
    ));

    return (
        <Modal
            visible={formVisible}
            title="Form Widget"
            okText="Submit"
            // onCancel={() => {
            // setFormVisible(false);
            // form.resetFields();
            // }}
            closeIcon={<Button icon={<CloseOutlined />} onClick={() => {
                setFormVisible(false);
                form.resetFields();
            }} />}
            footer={null}
        >
            <Spin spinning={queueState.isLoading || formState.isLoading}>
                {selectedFormfromMenu?.fields.form_functions && selectedFormfromMenu?.values && number && formFunction}

                <DynamicForm
                    value={selectedFormfromMenu?.fields}
                    number={number}
                    selectedFormfromMenu={selectedFormfromMenu}
                    setFormVisible={setFormVisible}
                    form={form}
                    setOutgoingDialed={setOutgoingDialed}
                />
            </Spin>
        </Modal>
    );
};

const DynamicForm = ({
    value,
    index,
    form,
    number,
    selectedFormfromMenu,
    setFormVisible,
    setOutgoingDialed,
}) => {
    const { callId } = useSelector((state) => state.CallReducer);
    const { outgoingCallId } = useSelector((state) => state.OutgoingCallReducer);
    const [date, setDate] = useState();

    useEffect(() => {
        const initialValues = {};
        if (selectedFormfromMenu?.values) {
            value?.form_fields.forEach((field) => {
                if (field.type !== "date") {
                    initialValues[field.name] =
                        selectedFormfromMenu?.values?.data[field?.name];
                } else {
                    const dateValue = selectedFormfromMenu?.values?.data[field?.name];
                    if (dateValue) {
                        if (Array.isArray(dateValue)) {
                            dateValue.forEach(
                                (date) =>
                                (initialValues[field.name] = moment(
                                    moment(date).format("YYYY-MM-DDTHH:mm:ss.SSSZ")
                                ))
                            );
                        } else {
                           // console.log("dateValue : " + dateValue)
                            initialValues[field.name] = moment(
                                moment(dateValue).format("YYYY-MM-DDTHH:mm:ss.SSSZ")
                            );
                        }
                    }
                }
            });
            form.setFieldsValue(initialValues);
        } else if (!selectedFormfromMenu?.values && number) {
            form.setFieldsValue({ phone_number: number });
        }
        if (callId) form.setFieldsValue({ call_id: callId });
        else if (outgoingCallId) form.setFieldsValue({ call_id: outgoingCallId });
    }, [selectedFormfromMenu, value, outgoingCallId, callId, form]);

    // useEffect(() => {
    //     if (outgoingCallId) {
    //         form.setFieldsValue({ call_id: outgoingCallId });
    //     }
    // }, [outgoingCallId]);

    const dispatch = useDispatch();
    const formDataState = useSelector((state) => state.FormDataReducer);

    useEffect(() => {
        if (formDataState.errMess) {
            openNotificationWithIcon(formDataState.errMess);
        }
    }, [formDataState.errMess]);

    useEffect(() => {
        if (formDataState.message) {
            openSuccessNotificationWithIcon(formDataState.message);
        }
    }, [formDataState.message]);

    const [fields, setFields] = useState([])
    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={(values) =>
                form
                    .validateFields()
                    .then((values) => {
                        const formData = {};
                        for (const key in values) {
                            if (values.hasOwnProperty(key)) {
                                const value = values[key];
                                if (moment.isMoment(value)) {
                                    // Check if the value is a moment.js object
                                    formData[key] = value; // Add the moment.js object to the form data
                                } else {
                                    formData[key] = value; // Add other form fields to the form data
                                }
                            }
                        }
                        dispatch(
                            postFormData({
                                ...formData,
                                form_id: selectedFormfromMenu.fields.id,
                            })
                        );
                        setOutgoingDialed(false)
                        form.resetFields();
                        setFormVisible(false);
                    })
                    .then(() => {
                        form.resetFields(value?.form_fields.map((val) => val.name));
                        setDate();
                    })
                    .catch((e) => console.log(e))
            }
        >
            <Form.Item
                label="Call ID"
                name="call_id"
                rules={[
                    {
                        required: true,
                        message: 'Call Id is required!'
                    }
                ]}
            >
                <Input disabled />
            </Form.Item>

            {value?.form_fields &&
                value?.form_fields.map((val, ind) => {
                    return (
                        <>
                            <RenderItem
                                key={val.name}
                                item={val}
                                data={selectedFormfromMenu?.values}
                                date={date}
                                setDate={setDate}
                            />
                        </>
                    );
                })}

            <Form.Item>
                <Button
                    loading={formDataState.isLoading}
                    icon={<SaveOutlined />}
                    type="primary"
                    htmlType="submit"
                >
                    Submit
                </Button>
            </Form.Item>
            <Divider dashed />
        </Form>
    );
};

const RenderItem = ({ item, data, date, setDate }) => {
    switch (item.type) {
        default:
        case "input":
            if (item.appendable === 1) {
                return (
                    <Form.List
                        name={item.name}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            //   name={[item.name, index]}
                                            name={[field.name]}
                                            label={`${item.label} ${index + 1}`}
                                            required={item.required}
                                            key={field.key}
                                            rules={[
                                                {
                                                    required: item.required == 11 && true,
                                                    message: `${item.name} is required`
                                                }
                                            ]}
                                        >
                                            <Input disabled={item.editable ? false : index !== fields.length - 1} />
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add();
                                            }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add {item.label}
                                        </Button>
                                    </Form.Item>
                                </>
                            );
                        }}
                    </Form.List>
                );
            } else {
                return (
                    <Form.Item
                        name={item.name}
                        key={item.label}
                        label={item.label}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        <Input disabled={item.editable ? false : data ? true : item.name === 'phone_number' ? true : false} />
                    </Form.Item>
                );
            }
        case "date":
            if (item.appendable === 1) {
                return (
                    <Form.List
                        name={item.name}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            name={[field.name]}
                                            label={`${item.label} ${index + 1}`}
                                            key={field.key}
                                            rules={[
                                                {
                                                    required: item.required == 1 && true,
                                                    message: `${item.name} is required`
                                                }
                                            ]}
                                        >
                                            <DatePicker
                                                disabled={item.editable ? false : index !== fields.length - 1}
                                                style={{ width: "100%" }}
                                                onChange={(date, dateString) => setDate(dateString)}
                                            />
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add();
                                            }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add {item.label}
                                        </Button>
                                    </Form.Item>
                                </>
                            );
                        }}
                    </Form.List>
                );
            } else {
                return (
                    <Form.Item
                        name={item.name}
                        key={item.label}
                        label={item.label}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        <DatePicker
                            disabled={item.editable ? false : data ? true : false}
                            style={{ width: "100%" }}
                            onChange={(date, dateString) => setDate(dateString)}
                        />
                    </Form.Item>
                );
            }
        case "checkbox":
            if (item.appendable === 1) {
                return (
                    <Form.List
                        name={item.name}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            name={[field.name]}
                                            label={`${item.label} ${index + 1}`}
                                            key={field.key}
                                            rules={[
                                                {
                                                    required: item.required == 1 && true,
                                                    message: `${item.name} is required`
                                                }
                                            ]}
                                        >
                                            <Checkbox.Group
                                                key={item.id}
                                                disabled={item.editable ? false : index !== fields.length - 1}
                                            >
                                                {item.form_field_options &&
                                                    item.form_field_options.map((value, index) => (
                                                        <Checkbox value={value.label} key={value.id}>
                                                            {value.label}
                                                        </Checkbox>
                                                    ))}
                                            </Checkbox.Group>
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add();
                                            }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add {item.label}
                                        </Button>
                                    </Form.Item>
                                </>
                            );
                        }}
                    </Form.List>
                );
            } else {
                return (
                    <Form.Item
                        name={item.name}
                        key={item.label}
                        label={item.label}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        <Checkbox.Group key={item.id}>
                            {item.form_field_options &&
                                item.form_field_options.map((value, index) => (
                                    <Checkbox value={value.label} key={value.id} disabled={item.editable ? false : data ? true : false}>
                                        {value.label}
                                    </Checkbox>
                                ))}
                        </Checkbox.Group>
                    </Form.Item>
                );
            }

        case "radio":
            if (item.appendable === 1) {
                return (
                    <Form.List
                        name={item.name}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            name={[field.name]}
                                            label={`${item.label} ${index + 1}`}
                                            key={field.key}
                                            rules={[
                                                {
                                                    required: item.required == 1 && true,
                                                    message: `${item.name} is required`
                                                }
                                            ]}
                                        >
                                            <Radio.Group
                                                key={item.id}
                                                disabled={item.editable ? false : index !== fields.length - 1}
                                            >
                                                {item.form_field_options &&
                                                    item.form_field_options.map((value, index) => (
                                                        <Radio
                                                            value={value.label}
                                                            key={value.id}
                                                        >
                                                            {value.label}
                                                        </Radio>
                                                    ))}
                                            </Radio.Group>
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add();
                                            }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add {item.label}
                                        </Button>
                                    </Form.Item>
                                </>
                            );
                        }}
                    </Form.List>
                );
            } else {
                return (
                    <Form.Item
                        name={item.name}
                        key={item.label}
                        label={item.label}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        <Radio.Group key={item.id}>
                            {item.form_field_options &&
                                item.form_field_options.map((value, index) => (
                                    <Radio value={value.label} key={value.id} disabled={item.editable ? false : data ? true : false}>
                                        {value.label}
                                    </Radio>
                                ))}
                        </Radio.Group>
                    </Form.Item>
                );
            }
        case "textarea":
            if (item.appendable === 1) {
                return (
                    <Form.List
                        name={item.name}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            //   name={[item.name, index]}
                                            name={[field.name]}
                                            label={`${item.label} ${index + 1}`}
                                            required={item.required}
                                            key={field.key}
                                        >
                                            <Input.TextArea disabled={item.editable ? false : index !== fields.length - 1} />
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add();
                                            }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add {item.label}
                                        </Button>
                                    </Form.Item>
                                </>
                            );
                        }}
                    </Form.List>
                );
            } else {
                return (
                    <Form.Item
                        name={item.name}
                        key={item.label}
                        label={item.label}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        <Input.TextArea key={item.id} disabled={item.editable ? false : data ? true : false} />
                    </Form.Item>
                );
            }

        case "select":
            if (item.appendable === 1) {
                return (
                    <Form.List
                        name={item.name}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            //   name={[item.name, index]}
                                            name={[field.name]}
                                            label={`${item.label} ${index + 1}`}
                                            // label={
                                            //     <Space>
                                            //         {" "}
                                            //         <MinusCircleOutlined
                                            //             onClick={() =>
                                            //                 setFields((fields) =>
                                            //                     fields.filter((field) => field.name !== value.name)
                                            //                 )
                                            //             }
                                            //         />
                                            //     </Space>
                                            // }

                                            key={field.key}
                                            rules={[
                                                {
                                                    required: item.required == 1 && true,
                                                    message: `${item.name} is required`
                                                }
                                            ]}


                                        >
                                            {/* <Input.TextArea disabled={item.editable ? false : index !== fields.length - 1} /> */}
                                            <Select
                                                showSearch
                                                optionFilterProp="children"
                                                // mode="tags"
                                                disabled={item.editable ? false : index !== fields.length - 1}>
                                                {item.form_field_options &&
                                                    item.form_field_options.map((v, i) => (
                                                        <Select.Option key={v.label} value={v.label}>
                                                            {v.label}
                                                        </Select.Option>
                                                    ))}
                                            </Select>
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add();
                                            }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add {item.label}
                                        </Button>
                                    </Form.Item>
                                </>
                            );
                        }}
                    </Form.List>
                );
            } else {
                return (
                    <Form.Item
                        name={item.name}
                        key={item.label}
                        label={item.label}
                        rules={[
                            {
                                required: item.required == 1 && true,
                                message: `${item.name} is required`
                            }
                        ]}
                        style={{ display: item.hideable && "none" }}
                    >
                        <Select
                            optionFilterProp="children"
                            showSearch
                            disabled={item.editable ? false : data ? true : false}>
                            {item.form_field_options &&
                                item.form_field_options.map((v, i) => (
                                    <Select.Option key={v.label} value={v.label}>
                                        {v.label}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                );
            }
    }
};
