import { <PERSON><PERSON>, Card, Select, Space, Spin, Table } from "antd";
import { useEffect, useState, useCallback } from "react";
import openNotificationWithIcon from "./Notification";
import apiClient from "../config/apiClient";

export const ScheduleCallBackTable = ({ setNumber, setRedialClick, setDialerVisible, setRedialId }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [updatingId, setUpdatingId] = useState(null);
    const uId = sessionStorage.getItem("id");

    const [paginate, setPaginate] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });


    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const response = await apiClient.get(`/api/callback?agent=${uId}`, {
                headers: {
                    Accept: "application/json",
                    Authorization: `Bearer ${sessionStorage.getItem("agent_token")}`,
                },
            });

            const todayRecords = (response.data.today?.records || []).map((r) => ({
                ...r,
                key: r.id,
                category: "today",
            }));

            const yesterdayRecords = (response.data.yesterday?.records || []).map((r) => ({
                ...r,
                key: r.id,
                category: "yesterday",
            }));

            const overdueRecords = (response.data.overdue?.records || []).map((r) => ({
                ...r,
                key: r.id,
                category: "overdue",
            }));

            const combinedRecords = [
                ...todayRecords,
                ...yesterdayRecords,
                ...overdueRecords,
            ];

            setData(combinedRecords);
            setPaginate((prev) => ({
                ...prev,
                total: combinedRecords.length,
            }));
        } catch (err) {
            openNotificationWithIcon("error", err.response?.data?.message || "Failed To Schedule Call Back Data");
            setData([]);
            setPaginate((prev) => ({ ...prev, total: 0 }));
        } finally {
            setLoading(false);
        }
    }, [uId]);

    useEffect(() => {
        if (uId) {
            fetchData();
        }
    }, [uId, fetchData]);

    const handleStatusChange = async (value, record) => {
        setNumber(record.number);
        setRedialClick(true);
        setDialerVisible(true);
        setRedialId(record.id);
        // setUpdatingId(record.id);
        // try {
        //     await apiClient.patch(
        //         `/api/callback/status`,
        //         {
        //             id: record.id,
        //             agent: uId,
        //             status: value,
        //         },
        //         {
        //             headers: {
        //                 Accept: "application/json",
        //                 Authorization: `Bearer ${sessionStorage.getItem("agent_token")}`,
        //             },
        //         }
        //     );
        //     openNotificationWithIcon("success", "Status updated successfully");
        //     fetchData();
        // } catch (err) {
        //     openNotificationWithIcon("error", err.response?.data?.message || "Failed to update status");
        // } finally {
        //     setUpdatingId(null);
        // }
    };


    const columnsCallBack = [
        {
            title: "Number",
            dataIndex: "number",
            key: "number",
        },
        {
            title: "Date",
            dataIndex: "date",
            key: "date",
            render: (text) => new Date(text).toLocaleString(),
        },
        {
            title: "Category",
            dataIndex: "category",
            key: "category",
            render: (val) => (
                <span style={{ textTransform: "capitalize" }}>{val}</span>
            ),
        },
        {
            title: "Action",
            dataIndex: "status",
            key: "status",
            render: (text, record) => {
                return (
                    <Space>
                        {updatingId === record.id ? (
                            <Spin size="small" />
                        ) : (
                            <Select
                                value={text}
                                onChange={(val) => handleStatusChange(val, record)}
                                style={{ width: 120 }}
                            >
                                <Select.Option value="pending">Pending</Select.Option>
                                <Select.Option value="completed">Completed</Select.Option>
                                <Select.Option value="cancelled">Cancelled</Select.Option>
                                <Select.Option value="redial">Redial</Select.Option>
                            </Select>
                        )}

                        <Button
                            onClick={() => {
                                handleStatusChange("redial", record);
                            }}
                        >
                            Redial
                        </Button>
                    </Space>
                )
            },
        },
    ];

    const handlePaginationChange = (page, pageSize) => {
        setPaginate((prev) => ({
            ...prev,
            current: page,
            pageSize: pageSize,
        }));
    };

    return (
        <>
            <br />
            <Card title="Schedule Callback Requests">
                <Table
                    key={(record, index) => index}
                    columns={columnsCallBack}
                    dataSource={data}
                    rowClassName={(record) => {
                        if (record.category === "overdue") return "row-overdue";
                        if (record.category === "yesterday") return "row-yesterday";
                    }}
                    pagination={{
                        current: paginate.current,
                        pageSize: paginate.pageSize,
                        total: paginate.total,
                        showSizeChanger: true,
                        onChange: handlePaginationChange,
                    }}
                    loading={loading}
                />
            </Card>

            <style>{`
                .row-overdue {
                    background-color: #ffe6e6 !important;
                    color: red;
                    font-weight: 600;
                }
                .row-yesterday {
                    background-color: #ffebcc !important;
                    color: black;
                    font-weight: 600;
                }
            `}</style>
        </>
    );
};
