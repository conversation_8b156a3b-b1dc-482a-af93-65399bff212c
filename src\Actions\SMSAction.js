import apiClient from "../config/apiClient";
import { handleError } from "../Shared/handleError";


export const getSMSCategory = () => dispatch => {
    dispatch({ type: "SMS_LOADING" })
    apiClient.get(`/api/sMSCategory`).then(r => dispatch({ type: "SMS_CATEGORY", payload: r.data })).catch(e => dispatch({ type: "SMS_FAILED", payload: handleError(e) }))
}

export const getSMSByCategory = id => dispatch => {
    dispatch({ type: "SMS_LOADING" })
    apiClient.get(`/api/sMSCategory/${id}`).then(r => dispatch({ type: "SMS", payload: r.data })).catch(e => dispatch({ type: "SMS_FAILED", payload: handleError(e) }))
}

export const sendSMS = data => dispatch => {
    dispatch({ type: "SMS_LOADING" })
    apiClient.post(`/api/send-sms`, data).then(r => dispatch({ type: "SMS_SUCCESS", payload: r.data })).catch(e => dispatch({ type: "SMS_FAILED", payload: handleError(e) }))
}