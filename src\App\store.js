import {combineReducers, createStore, compose, applyMiddleware} from "redux"
import { FormWidgetReducer } from "../Reducers/FormWidgetReducer"
import { QueueReducer } from "../Reducers/QueueReducer"
import { CallReducer } from "../Reducers/CallReducer"
import { FormDataReducer } from "../Reducers/FormDataReducer"
import { WorkcodeReducer } from "../Reducers/WorkcodeReducer"
import { OutgoingCallReducer } from "../Reducers/OutgoingCallReducer"
import { logger } from 'redux-logger'
import thunk from "redux-thunk"
import {ScriptReducer} from "../Reducers/ScriptReducer";
import { SMSReducer } from "../Reducers/SMSReducer"
import { CallReminderReducer } from "../Reducers/CallReminderReducer"
import { CallReportFormReducer } from "../Reducers/CallReportFormReducer"

export const ConfigureStore = () =>{
    const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose
    return createStore(combineReducers({
        QueueReducer,
        FormWidgetReducer,
        CallReducer,
        FormDataReducer,
        WorkcodeReducer,
        OutgoingCallReducer,
        CallReminderReducer,
        CallReportFormReducer,
        ScriptReducer,
        SMSReducer
    }), composeEnhancers(applyMiddleware(thunk))) // thunk, logger
}