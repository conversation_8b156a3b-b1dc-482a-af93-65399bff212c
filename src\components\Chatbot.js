import React, { useState, useEffect, useRef, useCallback } from "react";
import { IoClose, IoSend, IoTrash } from "react-icons/io5";
import axios from "axios";
import apiClient from "../config/apiClient";
import Echo from "laravel-echo";
import Pusher from "pusher-js";
import { NotificationContainer, NotificationManager } from "react-notifications";
import "react-notifications/lib/notifications.css";

const DeleteModal = ({ onConfirm, onCancel }) => {
    return (
        <div style={modalOverlayStyle}>
            <div style={modalContentStyle}>
                <p>Are you sure you want to delete this conversation?</p>
                <div style={{ display: "flex", justifyContent: "space-evenly" }}>
                    <button style={modalCancelButtonStyle} onClick={onCancel}>
                        Cancel
                    </button>
                    <button style={modalDeleteButtonStyle} onClick={onConfirm}>
                        Delete
                    </button>
                </div>
            </div>
        </div>
    );
};

const Chatbot = ({ onClose, fetchUnreadCounts }) => {
    const [userId, setUserId] = useState(null);
    const [connectedUsers, setConnectedUsers] = useState([]);
    const [conversationUsers, setConversationUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);
    const [conversationId, setConversationId] = useState(null);
    const [messages, setMessages] = useState([]); // All messages for the selected conversation
    const [input, setInput] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [typingUser, setTypingUser] = useState(null);
    const chatContainerRef = useRef(null);
    const [unreadMessages, setUnreadMessages] = useState({});

    const [searchTerm, setSearchTerm] = useState("");
    const filteredUsers = connectedUsers.filter((user) =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sorted filtered users based on unread count and updated_at timestamp.
    const sortedFilteredUsers = filteredUsers.slice().sort((a, b) => {
        const convA = conversationUsers.find(
            (conv) =>
                (conv.user_one === a.id && conv.user_two === userId) ||
                (conv.user_two === a.id && conv.user_one === userId)
        );
        const convB = conversationUsers.find(
            (conv) =>
                (conv.user_one === b.id && conv.user_two === userId) ||
                (conv.user_two === b.id && conv.user_one === userId)
        );
        // Use nullish coalescing so that a value of 0 is not overridden
        const unreadA = convA ? (unreadMessages[convA.id] ?? convA.unread_count) : 0;
        const unreadB = convB ? (unreadMessages[convB.id] ?? convB.unread_count) : 0;

        // Prioritize conversations with unread messages.
        if (unreadA > 0 && unreadB === 0) return -1;
        if (unreadB > 0 && unreadA === 0) return 1;

        // Otherwise, sort by updated_at timestamp (most recent first).
        const timeA = convA && convA.updated_at ? new Date(convA.updated_at).getTime() : 0;
        const timeB = convB && convB.updated_at ? new Date(convB.updated_at).getTime() : 0;
        return timeB - timeA;
    });

    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [conversationToDelete, setConversationToDelete] = useState(null);

    const echoRef = useRef(null);
    useEffect(() => {
        if (!echoRef.current) {
            window.Pusher = Pusher;
            echoRef.current = new Echo({
                broadcaster: "pusher",
                key: process.env.REACT_APP_PUSHER_KEY ?? "local",
                cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? "mt1",
                wsHost:
                    process.env.REACT_APP_SOCKET_APP_URL ??
                    "solutionsv3.tclcontactplus.com",
                wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
                forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true,
                disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
                encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
            });
        }
    }, []);

    const fetchAuthUser = useCallback(() => {
        apiClient
            .get("/api/get-user")
            .then((response) => setUserId(response.data.id))
            .catch((error) =>
                console.error("Error fetching user:", error.response?.data || error.message)
            );
    }, []);

    useEffect(() => {
        fetchAuthUser();
    }, [fetchAuthUser]);

    const getIpAddress = () => {
        return axios
            .get("https://api.ipify.org?format=json")
            .then((response) => response.data.ip)
            .catch((error) => {
                console.error("Error getting IP address:", error);
                return "";
            });
    };

    const fetchConnectedUsers = useCallback(() => {
        apiClient
            .get("/api/conversation/allUsers")
            .then((response) => setConnectedUsers(response.data))
            .catch((error) =>
                console.error(
                    "Error fetching connected users:",
                    error.response?.data || error.message
                )
            );
    }, []);

    useEffect(() => {
        fetchConnectedUsers();
    }, [fetchConnectedUsers]);

    
    const fetchConversationUsers = useCallback(() => {
        apiClient
            .get("/api/conversation/convUsers")
            .then((response) => {
                setConversationUsers(response.data);
                const unreadCounts = {};
                response.data.forEach((conv) => {
                    unreadCounts[conv.id] = conv.unread_count || 0;
                });
                setUnreadMessages(unreadCounts);
            })
            .catch((error) =>
                console.error(
                    "Error fetching conversation users:",
                    error.response?.data || error.message
                )
            );
    }, []);

    useEffect(() => {
        fetchConversationUsers();
    }, [fetchConversationUsers]);

    useEffect(() => {
        if (!userId || !echoRef.current) return;
        const unreadChannel = echoRef.current.channel(`unread_message_count.${userId}`);
        unreadChannel.listen("UnreadMessageCountUpdated", (event) => {
            setUnreadMessages((prev) => ({
                ...prev,
                [event.conversation_id]: event.total_unread,
            }));
            fetchConversationUsers();
        });
        return () => {
            echoRef.current.leaveChannel(`unread_message_count.${userId}`);
        };
    }, [userId, fetchConversationUsers]);

    // Open conversation and mark messages as read.
    const openConversation = useCallback(
        (convId) => {
            if (!convId) {
                console.error("Error: conversationId is undefined!");
                return;
            }
            setConversationId(convId);
            // Clear unread count locally.
            setUnreadMessages((prev) => ({
                ...prev,
                [convId]: 0,
            }));
            apiClient
                .post("/api/conversation/messages/mark-as-read", {
                    conversation_id: convId,
                })
                .then(() => {
                    // Update local conversationUsers to reflect the change.
                    setConversationUsers((prevConv) =>
                        prevConv.map((conv) =>
                            conv.id === convId ? { ...conv, unread_count: 0 } : conv
                        )
                    );
                    fetchUnreadCounts();
                })
                .catch((error) =>
                    console.error(
                        "Error marking messages as read:",
                        error.response?.data || error.message
                    )
                );
        },
        [fetchUnreadCounts]
    );

    const fetchMessages = useCallback(
        (convId) => {
            apiClient
                .get(`/api/conversation/chatroom/${convId}`)
                .then((response) => {
                    console.log("Fetched messages:", response.data);
                    setMessages(
                        response.data.map((msg) => ({
                            ...msg,
                            sender: msg.sender_id === userId ? "user" : "other",
                        }))
                    );
                })
                .catch((error) =>
                    console.error("Error fetching chat messages:", error)
                );
        },
        [userId]
    );

    // Fetch messages when either the selected user changes or conversationId changes.
    useEffect(() => {
        if (selectedUser && conversationId) {
            fetchMessages(conversationId);
        }
    }, [selectedUser, conversationId, fetchMessages]);

    // Also fetch messages when conversationId changes (if coming from openConversation)
    useEffect(() => {
        if (conversationId) {
            fetchMessages(conversationId);
        }
    }, [conversationId, fetchMessages]);

    useEffect(() => {
        if (chatContainerRef.current) {
            chatContainerRef.current.scrollTo({
                top: chatContainerRef.current.scrollHeight,
                behavior: "smooth",
            });
        }
    }, [messages]);

    const handleUserSelect = useCallback(
        (user) => {
            setSelectedUser(user);
            // Instead of clearing messages immediately, let fetchMessages update them.
            apiClient
                .post(`/api/conversation/create/${user.id}`)
                .then((response) => {
                    const convId = response.data.conversation_id;
                    setConversationId(convId);
                    openConversation(convId); // marks messages as read etc.
                    // Directly fetch messages even if conversationId doesn't change
                    fetchMessages(convId);
                })
                .catch((error) =>
                    console.error(
                        "Error creating conversation:",
                        error.response?.data || error.message
                    )
                );
        },
        [fetchMessages, openConversation]
    );

    useEffect(() => {
        if (!conversationId || !echoRef.current) return;
        const channelName = `message_channel.${conversationId}`;
        const channel = echoRef.current.channel(channelName);
        channel.listen("MessageSent", (event) => {
            console.log("New message received:", event);
            if (event.message.sender_id === userId) return;
            setMessages((prevMessages) => {
                const exists = prevMessages.some((msg) => msg.id === event.message.id);
                if (!exists) {
                    return [
                        ...prevMessages,
                        {
                            id: event.message.id,
                            text: event.message.message,
                            sender: "other",
                            sender_id: event.message.sender_id,
                            created_at: event.message.created_at,
                        },
                    ];
                }
                return prevMessages;
            });
            setUnreadMessages((prev) => ({
                ...prev,
                [conversationId]: (prev[conversationId] || 0) + 1,
            }));
        });
        return () => {
            echoRef.current.leaveChannel(channelName);
        };
    }, [conversationId, userId]);

    const handleSendMessage = useCallback(() => {
        if (!input.trim()) return;
        if (!userId) {
            console.error("User ID not found, cannot send message.");
            return;
        }
        if (!conversationId) {
            console.error("No conversation selected.");
            return;
        }
        setIsLoading(true);
        getIpAddress()
            .then((ipAddress) => {
                console.log("Sending message:", {
                    message: input,
                    sender_id: userId,
                    to: conversationId,
                    ipAddress,
                });
                return apiClient.post("/api/conversation/send", {
                    message: input,
                    sender_id: userId,
                    to: conversationId,
                    ipAddress,
                    status: "sent",
                });
            })
            .then(() => {
                setUnreadMessages((prev) => ({
                    ...prev,
                    [conversationId]: 0,
                }));
                setMessages((prevMessages) => [
                    ...prevMessages,
                    {
                        text: input,
                        sender: "user",
                        user_id: userId,
                        created_at: new Date(), // Adjust timestamp as needed.
                    },
                ]);
                console.log("Message added to UI:", {
                    text: input,
                    sender: "user",
                    sender_id: userId,
                });
                setInput("");
            })
            .catch((error) =>
                console.error("Error sending message:", error.response?.data || error.message)
            )
            .finally(() => {
                setIsLoading(false);
            });
    }, [input, userId, conversationId]);

    const handleDeleteConversation = useCallback((convId) => {
        setConversationToDelete(convId);
        setShowDeleteModal(true);
    }, []);

    const confirmDeleteConversation = useCallback(() => {
        apiClient
            .post("/api/conversation/chat/delete", {
                conversation_id: conversationToDelete,
            })
            .then(() => {
                setShowDeleteModal(false);
                setSelectedUser(null);
                setConversationId(null);
                setMessages([]);
                NotificationManager.success("Chat has been deleted", "Success", 3000);
            })
            .catch((error) => {
                console.error(
                    "Error deleting conversation:",
                    error.response?.data || error.message
                );
                NotificationManager.error("Failed to delete conversation", "Error", 3000);
                setShowDeleteModal(false);
            });
    }, [conversationToDelete]);

    const cancelDeleteConversation = useCallback(() => {
        setShowDeleteModal(false);
        setConversationToDelete(null);
    }, []);

    return (
        <div style={chatboxStyle}>
            <div style={chatHeaderStyle}>
                <span>{selectedUser ? selectedUser.username : "Chat"}</span>
                <div style={{ display: "flex", gap: "10px" }}>
                    {/* Uncomment below if you wish to allow deletion */}
                    {/* {conversationId && (
                        <IoTrash
                            style={deleteIconStyle}
                            onClick={() => handleDeleteConversation(conversationId)}
                        />
                    )} */}
                    <IoClose style={closeIconStyle} onClick={onClose} />
                </div>
            </div>
            <div style={chatMainContainerStyle}>
                {/* Sidebar */}
                <div style={sidebarStyle}>
                    <div style={searchBoxContainerStyle}>
                        <input
                            type="text"
                            placeholder="Search chats"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            style={searchBoxStyle}
                        />
                    </div>
                    <div style={userListStyle}>
                        {sortedFilteredUsers.map((user) => {
                            const conversation = conversationUsers.find(
                                (conv) =>
                                    (conv.user_one === user.id && conv.user_two === userId) ||
                                    (conv.user_two === user.id && conv.user_one === userId)
                            );
                            const unreadCount = conversation
                                ? (unreadMessages[conversation.id] ?? conversation.unread_count)
                                : 0;
                            return (
                                <div
                                    key={user.id}
                                    style={userItemStyle}
                                    onClick={() => handleUserSelect(user)}
                                >
                                    <div
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                            gap: "8px",
                                            width: "100%",
                                        }}
                                    >
                                        <span
                                            style={{
                                                flex: 1,
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                whiteSpace: "nowrap",
                                                fontWeight: unreadCount > 0 ? "bold" : "normal",
                                            }}
                                        >
                                            {user.username}
                                        </span>
                                        {unreadCount > 0 && (
                                            <span style={unreadBadgeStyle}>
                                                {unreadCount > 99 ? "99+" : unreadCount}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
                {/* Chat Content */}
                <div key={conversationId || "no-chat"} style={chatContentStyle}>
                    {selectedUser ? (
                        <>
                            <div style={chatMessagesStyle} ref={chatContainerRef}>
                                {messages.length > 0 ? (
                                    messages.map((msg, index) => {
                                        const isSentByUser = msg.user_id === userId;
                                        const messageStyle = isSentByUser
                                            ? userMessageStyle
                                            : otherUserMessageStyle;
                                        const formattedTime = msg.created_at
                                            ? new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                                            : '';
                                        return (
                                            <div key={msg.id || `msg-${index}`} style={messageStyle}>
                                                <p>{msg.message || msg.text}</p>
                                                <span style={timestampStyle}>{formattedTime}</span>
                                            </div>
                                        );
                                    })
                                ) : (
                                    <p style={{ textAlign: "center", color: "#999" }}>
                                        No Messages
                                    </p>
                                )}
                                {typingUser && (
                                    <div style={typingIndicatorStyle}>
                                        {typingUser} is typing...
                                    </div>
                                )}
                            </div>
                            <div style={chatInputContainerStyle}>
                                <input
                                    type="text"
                                    value={input}
                                    onChange={(e) => setInput(e.target.value)}
                                    onFocus={() => setTypingUser(null)}
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                            e.preventDefault();
                                            handleSendMessage();
                                        }
                                    }}
                                    style={chatInputStyle}
                                    placeholder="Type a message..."
                                />
                                <button
                                    onClick={handleSendMessage}
                                    style={sendButtonStyle}
                                    disabled={isLoading}
                                >
                                    <IoSend style={{ fontSize: "16px", color: "white" }} />
                                </button>
                            </div>
                        </>
                    ) : (
                        <div
                            style={{
                                flex: 1,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                color: "#999",
                            }}
                        >
                            <p>Select a chat to start conversation</p>
                        </div>
                    )}
                </div>
            </div>
            {showDeleteModal && (
                <DeleteModal
                    onConfirm={confirmDeleteConversation}
                    onCancel={cancelDeleteConversation}
                />
            )}
            <NotificationContainer position="top-right" />
        </div>
    );
};

const chatboxStyle = {
    width: "560px", // 280px sidebar + 280px chat area
    height: "480px",
    position: "fixed",
    bottom: "32px",
    right: "32px",
    backgroundColor: "rgba(255,255,255,0.95)",
    backdropFilter: "blur(8px)",
    borderRadius: "20px",
    boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
    display: "flex",
    flexDirection: "column",
    zIndex: 1001,
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
    border: "1px solid rgba(255,255,255,0.3)",
    overflow: "hidden",
};

const chatHeaderStyle = {
    backgroundColor: "rgba(16,163,127,0.95)",
    color: "#fff",
    padding: "16px 24px",
    fontSize: "15px",
    fontWeight: "500",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    backdropFilter: "blur(4px)",
    boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
};

const closeIconStyle = {
    width: "24px",
    height: "24px",
    cursor: "pointer",
    transition: "transform 0.2s ease",
    color: "rgba(255,255,255,0.8)",
};

const chatMainContainerStyle = {
    display: "flex",
    flex: 1,
    minHeight: 0,
};

const sidebarStyle = {
    width: "280px",
    borderRight: "1px solid #ccc",
    display: "flex",
    flexDirection: "column",
    minHeight: 0,
    overflow: "hidden",
};

const chatContentStyle = {
    width: "280px",
    display: "flex",
    flexDirection: "column",
    minHeight: 0,
    overflow: "hidden",
};

const searchBoxContainerStyle = {
    padding: "12px 24px",
    background: "rgba(249,249,249,0.8)",
};

const searchBoxStyle = {
    width: "100%",
    padding: "8px 12px",
    borderRadius: "20px",
    border: "1px solid #ccc",
    outline: "none",
};

const userListStyle = {
    flex: 1,
    overflowY: "auto",
    padding: "12px 0",
    background: "rgba(249,249,249,0.5)",
    minHeight: 0,
};

const userItemStyle = {
    padding: "12px 24px",
    cursor: "pointer",
    transition: "0.2s",
    backgroundColor: "transparent",
    display: "flex",
    alignItems: "center",
    gap: "12px",
};

const chatMessagesStyle = {
    flex: 1,
    overflowY: "auto",
    padding: "24px",
    background: "linear-gradient(180deg, rgba(249,249,249,0.6) 0%, rgba(255,255,255,0.8) 100%)",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    minHeight: 0,
};

const chatInputContainerStyle = {
    display: "flex",
    alignItems: "center",
    padding: "12px 24px",
    backgroundColor: "rgba(255,255,255,0.95)",
    gap: "12px",
    boxSizing: "border-box",
};

const chatInputStyle = {
    flex: 1,
    padding: "12px 16px",
    border: "none",
    borderRadius: "20px",
    outline: "none",
    fontSize: "14px",
    backgroundColor: "rgba(0,0,0,0.03)",
    boxSizing: "border-box",
};

const sendButtonStyle = {
    width: "150px",
    height: "50px",
    backgroundColor: "#10a37f",
    color: "white",
    border: "none",
    borderRadius: "25px",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: "transform 0.2s ease, background-color 0.2s ease",
    fontSize: "24px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
};

const typingIndicatorStyle = {
    fontSize: "13px",
    color: "#666",
    margin: "0 24px 12px",
    fontStyle: "italic",
    opacity: 0.8,
};

const messageBase = {
    padding: "12px 16px",
    borderRadius: "16px",
    maxWidth: "75%",
    fontSize: "14px",
    lineHeight: "1.5",
    transition: "transform 0.2s ease",
};

const userMessageStyle = {
    ...messageBase,
    backgroundColor: "#10a37f",
    color: "#fff",
    alignSelf: "flex-end",
    borderBottomRightRadius: "4px",
};

const otherUserMessageStyle = {
    ...messageBase,
    backgroundColor: "rgba(0,0,0,0.03)",
    color: "#333",
    alignSelf: "flex-start",
    borderBottomLeftRadius: "4px",
};

const deleteIconStyle = {
    width: "22px",
    height: "22px",
    cursor: "pointer",
    color: "rgba(255,255,255,0.8)",
    transition: "transform 0.2s ease, color 0.2s ease",
};

const unreadBadgeStyle = {
    backgroundColor: "#25D366",
    color: "#fff",
    fontSize: "12px",
    fontWeight: "bold",
    padding: "2px 6px",
    borderRadius: "50%",
    minWidth: "20px",
    height: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    textAlign: "center",
};

const modalOverlayStyle = {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 2000,
};

const modalContentStyle = {
    backgroundColor: "#fff",
    padding: "20px",
    borderRadius: "8px",
    width: "300px",
    textAlign: "center",
};

const modalDeleteButtonStyle = {
    padding: "8px 16px",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    backgroundColor: "red",
    color: "white",
};

const modalCancelButtonStyle = {
    padding: "8px 16px",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    backgroundColor: "#ccc",
    color: "black",
};

const timestampStyle = {
    fontSize: "10px",
    color: "black",
    textAlign: "right",
    marginTop: "4px",
    display: "block",
};

export default Chatbot;


// import React, { useState, useEffect, useRef, useCallback } from "react";
// import { IoClose, IoSend, IoTrash } from "react-icons/io5";
// import axios from "axios";
// import apiClient from "../config/apiClient";
// import Echo from "laravel-echo";
// import Pusher from "pusher-js";
// import { NotificationContainer, NotificationManager } from "react-notifications";
// import "react-notifications/lib/notifications.css";

// const DeleteModal = ({ onConfirm, onCancel }) => {
//     return (
//         <div style={modalOverlayStyle}>
//             <div style={modalContentStyle}>
//                 <p>Are you sure you want to delete this conversation?</p>
//                 <div style={{ display: "flex", justifyContent: "space-evenly" }}>
//                     <button style={modalCancelButtonStyle} onClick={onCancel}>
//                         Cancel
//                     </button>
//                     <button style={modalDeleteButtonStyle} onClick={onConfirm}>
//                         Delete
//                     </button>
//                 </div>
//             </div>
//         </div>
//     );
// };

// const Chatbot = ({ onClose, fetchUnreadCounts }) => {
//     const [userId, setUserId] = useState(null);
//     const [connectedUsers, setConnectedUsers] = useState([]);
//     const [conversationUsers, setConversationUsers] = useState([]);
//     const [selectedUser, setSelectedUser] = useState(null);
//     const [conversationId, setConversationId] = useState(null);
//     const [messages, setMessages] = useState([]); // All messages for the selected conversation
//     const [input, setInput] = useState("");
//     const [isLoading, setIsLoading] = useState(false);
//     const [typingUser, setTypingUser] = useState(null);
//     const chatContainerRef = useRef(null);
//     const [unreadMessages, setUnreadMessages] = useState({});

//     const [searchTerm, setSearchTerm] = useState("");
//     const filteredUsers = connectedUsers.filter((user) =>
//         user.username.toLowerCase().includes(searchTerm.toLowerCase())
//     );

//     // Sorted filtered users based on unread count and updated_at timestamp.
//     const sortedFilteredUsers = filteredUsers.slice().sort((a, b) => {
//         const convA = conversationUsers.find(
//             (conv) =>
//                 (conv.user_one === a.id && conv.user_two === userId) ||
//                 (conv.user_two === a.id && conv.user_one === userId)
//         );
//         const convB = conversationUsers.find(
//             (conv) =>
//                 (conv.user_one === b.id && conv.user_two === userId) ||
//                 (conv.user_two === b.id && conv.user_one === userId)
//         );
//         // Use nullish coalescing so that a value of 0 is not overridden
//         const unreadA = convA ? (unreadMessages[convA.id] ?? convA.unread_count) : 0;
//         const unreadB = convB ? (unreadMessages[convB.id] ?? convB.unread_count) : 0;

//         // Prioritize conversations with unread messages.
//         if (unreadA > 0 && unreadB === 0) return -1;
//         if (unreadB > 0 && unreadA === 0) return 1;

//         // Otherwise, sort by updated_at timestamp (most recent first).
//         const timeA = convA && convA.updated_at ? new Date(convA.updated_at).getTime() : 0;
//         const timeB = convB && convB.updated_at ? new Date(convB.updated_at).getTime() : 0;
//         return timeB - timeA;
//     });

//     const [showDeleteModal, setShowDeleteModal] = useState(false);
//     const [conversationToDelete, setConversationToDelete] = useState(null);

//     const echoRef = useRef(null);
//     useEffect(() => {
//         if (!echoRef.current) {
//             window.Pusher = Pusher;
//             echoRef.current = new Echo({
//                 broadcaster: "pusher",
//                 key: process.env.REACT_APP_PUSHER_KEY ?? "local",
//                 cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? "mt1",
//                 wsHost:
//                     process.env.REACT_APP_SOCKET_APP_URL ??
//                     "solutionsv3.tclcontactplus.com",
//                 wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
//                 forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true,
//                 disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
//                 encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
//             });
//         }
//     }, []);

//     const fetchAuthUser = useCallback(() => {
//         apiClient
//             .get("/api/get-user")
//             .then((response) => setUserId(response.data.id))
//             .catch((error) =>
//                 console.error("Error fetching user:", error.response?.data || error.message)
//             );
//     }, []);

//     useEffect(() => {
//         fetchAuthUser();
//     }, [fetchAuthUser]);

//     const getIpAddress = () => {
//         return axios
//             .get("https://api.ipify.org?format=json")
//             .then((response) => response.data.ip)
//             .catch((error) => {
//                 console.error("Error getting IP address:", error);
//                 return "";
//             });
//     };

//     const fetchConnectedUsers = useCallback(() => {
//         apiClient
//             .get("/api/conversation/allUsers")
//             .then((response) => setConnectedUsers(response.data))
//             .catch((error) =>
//                 console.error(
//                     "Error fetching connected users:",
//                     error.response?.data || error.message
//                 )
//             );
//     }, []);

//     useEffect(() => {
//         fetchConnectedUsers();
//     }, [fetchConnectedUsers]);

//     const fetchConversationUsers = useCallback(() => {
//         apiClient
//             .get("/api/conversation/convUsers")
//             .then((response) => {
//                 setConversationUsers(response.data);
//                 const unreadCounts = {};
//                 response.data.forEach((conv) => {
//                     unreadCounts[conv.id] = conv.unread_count || 0;
//                 });
//                 setUnreadMessages(unreadCounts);
//             })
//             .catch((error) =>
//                 console.error(
//                     "Error fetching conversation users:",
//                     error.response?.data || error.message
//                 )
//             );
//     }, []);

//     useEffect(() => {
//         fetchConversationUsers();
//     }, [fetchConversationUsers]);

//     useEffect(() => {
//         if (!userId || !echoRef.current) return;
//         const unreadChannel = echoRef.current.channel(`unread_message_count.${userId}`);
//         unreadChannel.listen("UnreadMessageCountUpdated", (event) => {
//             setUnreadMessages((prev) => ({
//                 ...prev,
//                 [event.conversation_id]: event.total_unread,
//             }));
//             fetchConversationUsers();
//         });
//         return () => {
//             echoRef.current.leaveChannel(`unread_message_count.${userId}`);
//         };
//     }, [userId, fetchConversationUsers]);

//     // Open conversation and mark messages as read.
//     const openConversation = useCallback(
//         (convId) => {
//             if (!convId) {
//                 console.error("Error: conversationId is undefined!");
//                 return;
//             }
//             setConversationId(convId);
//             // Clear unread count locally.
//             setUnreadMessages((prev) => ({
//                 ...prev,
//                 [convId]: 0,
//             }));
//             apiClient
//                 .post("/api/conversation/messages/mark-as-read", {
//                     conversation_id: convId,
//                 })
//                 .then(() => {
//                     // Update local conversationUsers to reflect the change.
//                     setConversationUsers((prevConv) =>
//                         prevConv.map((conv) =>
//                             conv.id === convId ? { ...conv, unread_count: 0 } : conv
//                         )
//                     );
//                     fetchUnreadCounts();
//                 })
//                 .catch((error) =>
//                     console.error(
//                         "Error marking messages as read:",
//                         error.response?.data || error.message
//                     )
//                 );
//         },
//         [fetchUnreadCounts]
//     );

//     const handleUserSelect = useCallback(
//         (user) => {
//             setSelectedUser(user);
//             setMessages([]);
//             apiClient
//                 .post(`/api/conversation/create/${user.id}`)
//                 .then((response) => {
//                     if (response.data.conversation_id) {
//                         setConversationId(response.data.conversation_id);
//                         openConversation(response.data.conversation_id);
//                     }
//                 })
//                 .catch((error) =>
//                     console.error(
//                         "Error creating conversation:",
//                         error.response?.data || error.message
//                     )
//                 );
//         },
//         [openConversation]
//     );

//     useEffect(() => {
//         if (!conversationId || !echoRef.current) return;
//         const channelName = `message_channel.${conversationId}`;
//         const channel = echoRef.current.channel(channelName);
//         channel.listen("MessageSent", (event) => {
//             console.log("New message received:", event);
//             if (event.message.sender_id === userId) return;
//             setMessages((prevMessages) => {
//                 const exists = prevMessages.some((msg) => msg.id === event.message.id);
//                 if (!exists) {
//                     return [
//                         ...prevMessages,
//                         {
//                             id: event.message.id,
//                             text: event.message.message,
//                             sender: "other",
//                             sender_id: event.message.sender_id,
//                             created_at: event.message.created_at,
//                         },
//                     ];
//                 }
//                 return prevMessages;
//             });
//             setUnreadMessages((prev) => ({
//                 ...prev,
//                 [conversationId]: (prev[conversationId] || 0) + 1,
//             }));
//         });
//         return () => {
//             echoRef.current.leaveChannel(channelName);
//         };
//     }, [conversationId, userId]);

//     const handleSendMessage = useCallback(() => {
//         if (!input.trim()) return;
//         if (!userId) {
//             console.error("User ID not found, cannot send message.");
//             return;
//         }
//         if (!conversationId) {
//             console.error("No conversation selected.");
//             return;
//         }
//         setIsLoading(true);
//         getIpAddress()
//             .then((ipAddress) => {
//                 console.log("Sending message:", {
//                     message: input,
//                     sender_id: userId,
//                     to: conversationId,
//                     ipAddress,
//                 });
//                 return apiClient.post("/api/conversation/send", {
//                     message: input,
//                     sender_id: userId,
//                     to: conversationId,
//                     ipAddress,
//                     status: "sent",
//                 });
//             })
//             .then(() => {
//                 setUnreadMessages((prev) => ({
//                     ...prev,
//                     [conversationId]: 0,
//                 }));
//                 setMessages((prevMessages) => [
//                     ...prevMessages,
//                     {
//                         text: input,
//                         sender: "user",
//                         user_id: userId,
//                         created_at: new Date(), // You may adjust this if your API returns a proper timestamp.
//                     },
//                 ]);
//                 console.log("Message added to UI:", {
//                     text: input,
//                     sender: "user",
//                     sender_id: userId,
//                 });
//                 setInput("");
//             })
//             .catch((error) =>
//                 console.error("Error sending message:", error.response?.data || error.message)
//             )
//             .finally(() => {
//                 setIsLoading(false);
//             });
//     }, [input, userId, conversationId]);

//     const fetchMessages = useCallback(
//         (convId) => {
//             apiClient
//                 .get(`/api/conversation/chatroom/${convId}`)
//                 .then((response) => {
//                     setMessages(
//                         response.data.map((msg) => ({
//                             ...msg,
//                             sender: msg.sender_id === userId ? "user" : "other",
//                         }))
//                     );
//                 })
//                 .catch((error) =>
//                     console.error("Error fetching chat messages:", error)
//                 );
//         },
//         [userId]
//     );

//     useEffect(() => {
//         if (!conversationId) return;
//         fetchMessages(conversationId);
//     }, [conversationId, fetchMessages]);

//     useEffect(() => {
//         if (chatContainerRef.current) {
//             chatContainerRef.current.scrollTo({
//                 top: chatContainerRef.current.scrollHeight,
//                 behavior: "smooth",
//             });
//         }
//     }, [messages]);

//     const handleDeleteConversation = useCallback((convId) => {
//         setConversationToDelete(convId);
//         setShowDeleteModal(true);
//     }, []);

//     const confirmDeleteConversation = useCallback(() => {
//         apiClient
//             .post("/api/conversation/chat/delete", {
//                 conversation_id: conversationToDelete,
//             })
//             .then(() => {
//                 setShowDeleteModal(false);
//                 setSelectedUser(null);
//                 setConversationId(null);
//                 setMessages([]);
//                 NotificationManager.success("Chat has been deleted", "Success", 3000);
//             })
//             .catch((error) => {
//                 console.error(
//                     "Error deleting conversation:",
//                     error.response?.data || error.message
//                 );
//                 NotificationManager.error("Failed to delete conversation", "Error", 3000);
//                 setShowDeleteModal(false);
//             });
//     }, [conversationToDelete]);

//     const cancelDeleteConversation = useCallback(() => {
//         setShowDeleteModal(false);
//         setConversationToDelete(null);
//     }, []);

//     return (
//         <div style={chatboxStyle}>
//             <div style={chatHeaderStyle}>
//                 <span>{selectedUser ? selectedUser.username : "Chat"}</span>
//                 <div style={{ display: "flex", gap: "10px" }}>
//                     {/* {conversationId && (
//                         <IoTrash
//                             style={deleteIconStyle}
//                             onClick={() => handleDeleteConversation(conversationId)}
//                         />
//                     )} */}
//                     <IoClose style={closeIconStyle} onClick={onClose} />
//                 </div>
//             </div>
//             <div style={chatMainContainerStyle}>
//                 {/* Sidebar */}
//                 <div style={sidebarStyle}>
//                     <div style={searchBoxContainerStyle}>
//                         <input
//                             type="text"
//                             placeholder="Search chats"
//                             value={searchTerm}
//                             onChange={(e) => setSearchTerm(e.target.value)}
//                             style={searchBoxStyle}
//                         />
//                     </div>
//                     <div style={userListStyle}>
//                         {sortedFilteredUsers.map((user) => {
//                             const conversation = conversationUsers.find(
//                                 (conv) =>
//                                     (conv.user_one === user.id && conv.user_two === userId) ||
//                                     (conv.user_two === user.id && conv.user_one === userId)
//                             );
//                             // Use nullish coalescing to check for zero and valid values.
//                             const unreadCount = conversation
//                                 ? (unreadMessages[conversation.id] ?? conversation.unread_count)
//                                 : 0;
//                             return (
//                                 <div
//                                     key={user.id}
//                                     style={userItemStyle}
//                                     onClick={() => handleUserSelect(user)}
//                                 >
//                                     <div
//                                         style={{
//                                             display: "flex",
//                                             alignItems: "center",
//                                             gap: "8px",
//                                             width: "100%",
//                                         }}
//                                     >
//                                         <span
//                                             style={{
//                                                 flex: 1,
//                                                 overflow: "hidden",
//                                                 textOverflow: "ellipsis",
//                                                 whiteSpace: "nowrap",
//                                                 fontWeight: unreadCount > 0 ? "bold" : "normal",
//                                             }}
//                                         >
//                                             {user.username}
//                                         </span>
//                                         {unreadCount > 0 && (
//                                             <span style={unreadBadgeStyle}>
//                                                 {unreadCount > 99 ? "99+" : unreadCount}
//                                             </span>
//                                         )}
//                                     </div>
//                                 </div>
//                             );
//                         })}
//                     </div>
//                 </div>
//                 {/* Chat Content */}
//                 <div style={chatContentStyle}>
//                     {selectedUser ? (
//                         <>
//                             <div style={chatMessagesStyle} ref={chatContainerRef}>
//                                 {messages.length > 0 ? (
//                                     messages.map((msg, index) => {
//                                         const isSentByUser = msg.user_id === userId;
//                                         const messageStyle = isSentByUser
//                                             ? userMessageStyle
//                                             : otherUserMessageStyle;
//                                         // Format the timestamp if available.
//                                         const formattedTime = msg.created_at
//                                             ? new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
//                                             : '';
//                                         return (
//                                             <div key={msg.id || `msg-${index}`} style={messageStyle}>
//                                                 <p>{msg.message || msg.text}</p>
//                                                 <span style={timestampStyle}>{formattedTime}</span>
//                                             </div>
//                                         );
//                                     })
//                                 ) : (
//                                     <p style={{ textAlign: "center", color: "#999" }}>
//                                         No Messages
//                                     </p>
//                                 )}
//                                 {typingUser && (
//                                     <div style={typingIndicatorStyle}>
//                                         {typingUser} is typing...
//                                     </div>
//                                 )}
//                             </div>
//                             <div style={chatInputContainerStyle}>
//                                 <input
//                                     type="text"
//                                     value={input}
//                                     onChange={(e) => setInput(e.target.value)}
//                                     onFocus={() => setTypingUser(null)}  // Clear typing indicator on focus
//                                     onKeyDown={(e) => {
//                                         if (e.key === "Enter") {
//                                             e.preventDefault();
//                                             handleSendMessage();
//                                         }
//                                     }}
//                                     style={chatInputStyle}
//                                     placeholder="Type a message..."
//                                 />
//                                 <button
//                                     onClick={handleSendMessage}
//                                     style={sendButtonStyle}
//                                     disabled={isLoading}
//                                 >
//                                     <IoSend style={{ fontSize: "16px", color: "white" }} />
//                                 </button>
//                             </div>
//                         </>
//                     ) : (
//                         <div
//                             style={{
//                                 flex: 1,
//                                 display: "flex",
//                                 alignItems: "center",
//                                 justifyContent: "center",
//                                 color: "#999",
//                             }}
//                         >
//                             <p>Select a chat to start conversation</p>
//                         </div>
//                     )}
//                 </div>
//             </div>
//             {showDeleteModal && (
//                 <DeleteModal
//                     onConfirm={confirmDeleteConversation}
//                     onCancel={cancelDeleteConversation}
//                 />
//             )}
//             <NotificationContainer position="top-right" />
//         </div>
//     );
// };

// const chatboxStyle = {
//     width: "560px", // 280px sidebar + 280px chat area
//     height: "480px",
//     position: "fixed",
//     bottom: "32px",
//     right: "32px",
//     backgroundColor: "rgba(255,255,255,0.95)",
//     backdropFilter: "blur(8px)",
//     borderRadius: "20px",
//     boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
//     display: "flex",
//     flexDirection: "column",
//     zIndex: 1001,
//     fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
//     border: "1px solid rgba(255,255,255,0.3)",
//     overflow: "hidden",
// };

// const chatHeaderStyle = {
//     backgroundColor: "rgba(16,163,127,0.95)",
//     color: "#fff",
//     padding: "16px 24px",
//     fontSize: "15px",
//     fontWeight: "500",
//     display: "flex",
//     justifyContent: "space-between",
//     alignItems: "center",
//     backdropFilter: "blur(4px)",
//     boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
// };

// const closeIconStyle = {
//     width: "24px",
//     height: "24px",
//     cursor: "pointer",
//     transition: "transform 0.2s ease",
//     color: "rgba(255,255,255,0.8)",
// };

// const chatMainContainerStyle = {
//     display: "flex",
//     flex: 1,
//     minHeight: 0,
// };

// const sidebarStyle = {
//     width: "280px",
//     borderRight: "1px solid #ccc",
//     display: "flex",
//     flexDirection: "column",
//     minHeight: 0,
//     overflow: "hidden",
// };

// const chatContentStyle = {
//     width: "280px",
//     display: "flex",
//     flexDirection: "column",
//     minHeight: 0,
//     overflow: "hidden",
// };

// const searchBoxContainerStyle = {
//     padding: "12px 24px",
//     background: "rgba(249,249,249,0.8)",
// };

// const searchBoxStyle = {
//     width: "100%",
//     padding: "8px 12px",
//     borderRadius: "20px",
//     border: "1px solid #ccc",
//     outline: "none",
// };

// const userListStyle = {
//     flex: 1,
//     overflowY: "auto",
//     padding: "12px 0",
//     background: "rgba(249,249,249,0.5)",
//     minHeight: 0,
// };

// const userItemStyle = {
//     padding: "12px 24px",
//     cursor: "pointer",
//     transition: "0.2s",
//     backgroundColor: "transparent",
//     display: "flex",
//     alignItems: "center",
//     gap: "12px",
// };

// const chatMessagesStyle = {
//     flex: 1,
//     overflowY: "auto",
//     padding: "24px",
//     background: "linear-gradient(180deg, rgba(249,249,249,0.6) 0%, rgba(255,255,255,0.8) 100%)",
//     display: "flex",
//     flexDirection: "column",
//     gap: "16px",
//     minHeight: 0,
// };

// const chatInputContainerStyle = {
//     display: "flex",
//     alignItems: "center",
//     padding: "12px 24px",
//     backgroundColor: "rgba(255,255,255,0.95)",
//     gap: "12px",
//     boxSizing: "border-box",
// };

// const chatInputStyle = {
//     flex: 1,
//     padding: "12px 16px",
//     border: "none",
//     borderRadius: "20px",
//     outline: "none",
//     fontSize: "14px",
//     backgroundColor: "rgba(0,0,0,0.03)",
//     boxSizing: "border-box",
// };

// const sendButtonStyle = {
//     width: "150px",
//     height: "50px",
//     backgroundColor: "#10a37f",
//     color: "white",
//     border: "none",
//     borderRadius: "25px",
//     cursor: "pointer",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     transition: "transform 0.2s ease, background-color 0.2s ease",
//     fontSize: "24px",
//     boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
// };

// const typingIndicatorStyle = {
//     fontSize: "13px",
//     color: "#666",
//     margin: "0 24px 12px",
//     fontStyle: "italic",
//     opacity: 0.8,
// };

// const messageBase = {
//     padding: "12px 16px",
//     borderRadius: "16px",
//     maxWidth: "75%",
//     fontSize: "14px",
//     lineHeight: "1.5",
//     transition: "transform 0.2s ease",
// };

// const userMessageStyle = {
//     ...messageBase,
//     backgroundColor: "#10a37f",
//     color: "#fff",
//     alignSelf: "flex-end",
//     borderBottomRightRadius: "4px",
// };

// const otherUserMessageStyle = {
//     ...messageBase,
//     backgroundColor: "rgba(0,0,0,0.03)",
//     color: "#333",
//     alignSelf: "flex-start",
//     borderBottomLeftRadius: "4px",
// };

// const deleteIconStyle = {
//     width: "22px",
//     height: "22px",
//     cursor: "pointer",
//     color: "rgba(255,255,255,0.8)",
//     transition: "transform 0.2s ease, color 0.2s ease",
// };

// const unreadBadgeStyle = {
//     backgroundColor: "#25D366",
//     color: "#fff",
//     fontSize: "12px",
//     fontWeight: "bold",
//     padding: "2px 6px",
//     borderRadius: "50%",
//     minWidth: "20px",
//     height: "20px",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     textAlign: "center",
// };

// const modalOverlayStyle = {
//     position: "fixed",
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: "rgba(0,0,0,0.5)",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     zIndex: 2000,
// };

// const modalContentStyle = {
//     backgroundColor: "#fff",
//     padding: "20px",
//     borderRadius: "8px",
//     width: "300px",
//     textAlign: "center",
// };

// const modalDeleteButtonStyle = {
//     padding: "8px 16px",
//     border: "none",
//     borderRadius: "4px",
//     cursor: "pointer",
//     backgroundColor: "red",
//     color: "white",
// };

// const modalCancelButtonStyle = {
//     padding: "8px 16px",
//     border: "none",
//     borderRadius: "4px",
//     cursor: "pointer",
//     backgroundColor: "#ccc",
//     color: "black",
// };

// const timestampStyle = {
//     fontSize: "10px",
//     color: "black",
//     textAlign: "right",
//     marginTop: "4px",
//     display: "block",
// };

// export default Chatbot;