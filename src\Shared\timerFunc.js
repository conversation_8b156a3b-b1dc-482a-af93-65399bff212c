
export const timerFunc = (date) =>{
    var startDateTime = new Date(date);
    var startStamp = startDateTime.getTime();

    var newDate = new Date();
    var newStamp = newDate.getTime();

    function updateClock() {
        newDate = new Date();
        newStamp = newDate.getTime();
        var diff = Math.round((newStamp-startStamp)/1000);
        
        var d = Math.floor(diff/(24*60*60)); /* though I hope she won't be working for consecutive days :) */
        diff = diff-(d*24*60*60);
        var h = Math.floor(diff/(60*60));
        diff = diff-(h*60*60);
        var m = Math.floor(diff/(60));
        diff = diff-(m*60);
        var s = diff;
        
        let time = {
            s : s < 10 ? `0${s}` : s,
            m : m < 10 ? `0${m}` : m,
            h : h < 10 ? `0${h}` : h
        }

        return time
    }

    return updateClock()
}