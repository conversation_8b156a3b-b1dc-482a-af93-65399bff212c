import apiClient from "../config/apiClient";
import {getAgentScript} from "../config/routes";
import initializeEcho from '../config/echo';
import Pusher from 'pusher-js';

export const fetchAgentScript = (queue, userId) => dispatch => {
    // const echo = initializeEcho(sessionStorage.getItem('agent_token'));
    
    // dispatch({type: "SCRIPT_LOADING"})
    // // apiClient.post(`${getAgentScript}`, {queue: queue}).then( r => {
    // //     dispatch({type: 'SCRIPT_SUCCESS', payload: r.data})
    // // })

    // const channel = echo.private(`agent-panel-getscriptbyqueue-channel.${userId}-${queue}`);
    // channel.listen(".agent-panel-getscriptbyqueue", (e) => {
    //     dispatch({type: 'SCRIPT_SUCCESS', payload: e.data.data})
    // });

    // return () => {
    //     channel.stopListening(".agent-panel-getscriptbyqueue");
    // };
}