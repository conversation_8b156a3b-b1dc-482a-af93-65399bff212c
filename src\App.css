@import "~antd/dist/antd.css";

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.site-layout-content {
  min-height: 100%;
  /* padding: 24px; */
}
.logo {
  float: left;
  width: 120px;
  height: 31px;
  margin: 16px 24px 16px 0;
  background: rgba(255, 255, 255, 0.3);
}
.ant-row-rtl #components-layout-demo-top .logo {
  float: right;
  margin: 16px 0 16px 24px;
}

.bg-image {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  background-image: url("./bg2.jpg");
  background-size: 100% 100%;
  background-position: right;
  background-repeat: no-repeat;
}
.bg-none {
  background: "#fff";
}
.top-heading {
  font-size: 32px;
  font-weight: 500;
  /* width: 100%; */
  text-align: center;
  color: #15347c;
  font-weight: bold;
  margin-bottom: 20px;
}
