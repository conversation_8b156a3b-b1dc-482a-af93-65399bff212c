import axios from 'axios';

const apiClient = axios.create({
    baseURL: process.env.REACT_APP_baseURL,
    withCredentials: false,
    headers: {
        'Accept': 'application/json',
    }
});

apiClient.interceptors.request.use((config) => {
    const token = sessionStorage.getItem('agent_token')

    if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
}, (error) => {
    return Promise.reject(error);
});

apiClient.interceptors.response.use((response) => {
    return response;

}, (error) => {
    if (error.response?.status === 401) {
        if (window.location.pathname == '/user/dialer') {
            sessionStorage.clear()
            window.location.href = "/user"
        }
    }

    return Promise.reject(error);
});

export default apiClient