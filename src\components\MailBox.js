// import { useEffect, useState, useRef } from "react";
// import {
//     MailOutlined,
//     InboxOutlined,
//     DownloadOutlined,
//     LeftOutlined,
//     RightOutlined,
//     PaperClipOutlined,
//     UploadOutlined,
// } from "@ant-design/icons";
// import axios from "axios";
// import { Button, Input, Upload, message, Typography, Spin, Collapse } from "antd";
// import DOMPurify from "dompurify";
// import { getEmails, replyEmail } from "../config/routes";

// const apiClient = axios.create({
//     baseURL: process.env.REACT_APP_baseURL,
// });

// // Custom hook to get window dimensions
// function useWindowSize() {
//     const [size, setSize] = useState({
//         width: window.innerWidth,
//         height: window.innerHeight,
//     });
//     useEffect(() => {
//         const handleResize = () =>
//             setSize({ width: window.innerWidth, height: window.innerHeight });
//         window.addEventListener("resize", handleResize);
//         return () => window.removeEventListener("resize", handleResize);
//     }, []);
//     return size;
// }

// export function MyModal({ closeModal }) {
//     const [emails, setEmails] = useState([]);
//     const [loading, setLoading] = useState(true);
//     const [selectedEmail, setSelectedEmail] = useState(null);
//     const [page, setPage] = useState(1);
//     const [paginationData, setPaginationData] = useState({
//         page: 1,
//         per_page: 5,
//         total: 0
//     });
//     const [sessionExpired, setSessionExpired] = useState(false);
//     const [replyMessage, setReplyMessage] = useState("");
//     const { width, height } = useWindowSize();
//     const showSidebar = width >= 889 && height >= 889;
//     const [fileList, setFileList] = useState([]);
//     const [initialLoad, setInitialLoad] = useState(true);
//     const token = sessionStorage.getItem("agent_token");

//     useEffect(() => {
//         if (initialLoad || page !== 1) {
//             fetchEmails();
//         }
//     }, [page]);

//     const fetchEmails = async () => {
//         if (!token) {
//             setSessionExpired(true);
//             setLoading(false);
//             return;
//         }
        
//         try {
//             setLoading(true);
//             const response = await apiClient.get(`${getEmails}${page}`, {
//                 headers: {
//                     Authorization: `Bearer ${token}`,
//                     Accept: "application/json",
//                 },
//             });
            
//             if (response.data) {
//                 setEmails(response.data.emails || []);
//                 setPaginationData({
//                     page: parseInt(response.data.page) || 1,
//                     per_page: parseInt(response.data.per_page) || 5,
//                     total: parseInt(response.data.total) || 0
//                 });
//                 setLoading(false);
//             }
//         } catch (error) {
//             console.error("Error fetching emails:", error);
//             if (error.response?.status === 401) {
//                 setSessionExpired(true);
//             }
//             setEmails([]);
//         } finally {
//             setLoading(false);
//         }
//     };

//     const changePage = (newPage) => {
//         // Validate page boundaries
//         const totalPages = Math.ceil(paginationData.total / paginationData.per_page);
//         if (newPage < 1 || newPage > totalPages) return;
        
//         setPage(newPage);
//     };

//     // Calculate total pages for pagination controls
//     const totalPages = Math.ceil(paginationData.total / paginationData.per_page);

//     // Reply Email
//     const handleReplySend = async () => {
//         if (!replyMessage.trim()) {
//             message.warning("Reply cannot be empty.");
//             return;
//         }

//         if (!selectedEmail?.message_id) {
//             message.error("No email selected to reply.");
//             return;
//         }

//         // console.log(selectedEmail.from);
//         // const email = selectedEmail.from.match(/<([^>]+)>/)[1];
        
//         const formData = new FormData();
//         formData.append("message_id", selectedEmail.message_id);
//         formData.append("email", selectedEmail.from);
//         formData.append("subject", selectedEmail.subject);
//         formData.append("body", replyMessage);

//         fileList.forEach(file => {
//             formData.append("attachments[]", file.originFileObj); // no change
//         });

//         if (!token) {
//             message.error("Session expired. Please login again.");
//             return;
//         }

//         try {
//             setLoading(true);
//             const response = await apiClient.post(`${replyEmail}`, formData, {
//                 headers: {
//                     Authorization: `Bearer ${token}`,
//                     "Content-Type": "multipart/form-data",
//                     Accept: "application/json",
//                 },
//             });

//             // Create a new reply object to add to the replies array
//             const newReply = {
//                 subject: `${selectedEmail.subject}`,
//                 from: `${selectedEmail.from}`,
//                 date: new Date().toISOString(),
//                 body: replyMessage,
//                 attachment: fileList.map(file => ({
//                     name: file.name,
//                     content: file.originFileObj ? "" : file.content
//                 })),
//                 message_id: response.data.message_id || Date.now().toString(),
//                 thread_id: selectedEmail.message_id
//             };

//             // Update the selectedEmail with the new reply
//             setSelectedEmail(prev => ({
//                 ...prev,
//                 replies: [...(prev.replies || []), newReply]
//             }));

//             message.success("Reply sent successfully!");
//             setReplyMessage("");
//             setFileList([]);
//         } catch (error) {
//             console.error("Reply send failed:", error);
//             if (error.response?.status === 401) {
//                 message.error("Unauthorized. Please login again.");
//                 setSessionExpired(true);
//             } else {
//                 message.error("Failed to send reply. Please try again.");
//             }
//         } finally {
//             setLoading(false);
//         }
//     };

//     const handleFileChange = ({ fileList }) => {
//         setFileList(fileList);
//     };

//     const { TextArea } = Input;
//     const { Title } = Typography;

//     const decodeBase64 = (base64String, fileName) => {
//         try {
//             const link = document.createElement("a");
//             link.href = `data:application/octet-stream;base64,${base64String}`;
//             link.download = fileName || "attachment";
//             document.body.appendChild(link);
//             link.click();
//             document.body.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading file:", error);
//             message.error("Failed to download attachment");
//         }
//     };

//     const sanitizeHTML = (html) => DOMPurify.sanitize(html);
//     function cleanEmailBody(rawHtml) {
//         let cleaned = rawHtml
//             .replace(/^>+ ?/gm, "")
//             .replace(/\n{2,}/g, "\n\n")
//             .trim();
//         return cleaned;
//     }
    

//     return (
//         <div style={styles.backdrop}>
//             <div style={styles.modal}>
//                 {showSidebar && (
//                     <div style={styles.sidebar}>
//                         <h3 style={styles.sidebarTitle}>
//                             <MailOutlined style={{ marginRight: 8 }} /> Mail
//                         </h3>
//                         <div
//                             style={{
//                                 ...styles.menuItem,
//                                 backgroundColor:
//                                     selectedEmail === null ? "#5f6368" : "#f1f3f4",
//                                 color: selectedEmail === null ? "#fff" : "#5f6368",
//                             }}
//                             onClick={() => setSelectedEmail(null)}
//                         >
//                             <InboxOutlined style={{ marginRight: 8 }} /> Inbox
//                         </div>
//                     </div>
//                 )}
//                 <div style={styles.content}>
//                     <div style={styles.header}>
//                         <h2 style={styles.headerTitle}>
//                             {selectedEmail ? "Email Details" : "Inbox"}
//                         </h2>
//                         <button onClick={closeModal} style={styles.closeButton}>
//                             Close
//                         </button>
//                     </div>
//                     {selectedEmail && (
//                         <button
//                             style={styles.backButton}
//                             onClick={() => setSelectedEmail(null)}
//                         >
//                             <LeftOutlined style={{ marginRight: 5 }} /> Back
//                         </button>
//                     )}
//                     {sessionExpired ? (
//                         <div style={styles.sessionExpired}>
//                             <p>
//                                 Session expired or login failed! Please try refreshing or logging
//                                 in again.
//                             </p>
//                         </div>
//                     ) : selectedEmail ? (
//                         <div style={styles.emailDetails}>
//                             <h3 style={styles.emailSubject}>{selectedEmail.subject}</h3>
//                             <p style={styles.emailFromDetail}>
//                                 <strong>From:</strong> {selectedEmail.from}
//                             </p>
//                             <p style={styles.emailDate}>
//                                 <strong>Date:</strong> {selectedEmail.date}
//                             </p>
//                             <p style={styles.emailBody}>
//                                 <strong>Body:</strong> {selectedEmail.body}
//                             </p>
//                             {
//                                 selectedEmail.attachment?.length > 0 && (
//                                     <div style={{ marginBottom: 15 }}>
//                                         <h4 style={{ marginBottom: 10, fontWeight: 700, marginTop: 15 }}>Attachments</h4>
//                                         {selectedEmail.attachment.map((file, index) => (
//                                             <div key={index} style={styles.attachment}>
//                                                 <PaperClipOutlined style={{ marginRight: 8, color: "#1a73e8" }} />
//                                                 <span>{file.name}</span>
//                                                 <DownloadOutlined
//                                                     style={{ cursor: "pointer", marginLeft: 10, color: "#1a73e8" }}
//                                                     onClick={() => decodeBase64(file.content, file.name)}
//                                                 />
//                                             </div>
//                                         ))}
//                                     </div>
//                                 )
//                             }

//                             {/* <Collapse defaultActiveKey={['1']}>
//                                 {[...(selectedEmail.replies || [])].map((reply, index) => (
//                                     <Collapse.Panel
//                                         key={index}
//                                         header={
//                                             <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
//                                                 <span>{reply?.from}</span>
//                                                 <span>{reply?.date}</span>
//                                             </div>
//                                         }
//                                     >
//                                         <div dangerouslySetInnerHTML={{ __html: cleanEmailBody(reply?.body) }} />
//                                         {
//                                             reply.attachment && reply.attachment.length > 0 && (
//                                                 <div style={{ marginTop: '15px' }}>
//                                                     <strong>Attachments:</strong>
//                                                     <ul style={{ listStyle: 'none', paddingLeft: 0 }}>
//                                                         {reply.attachment.map((file, idx) => (
//                                                             <li key={idx} style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
//                                                                 <PaperClipOutlined style={{ marginRight: 8, color: "#1a73e8" }} />
//                                                                 <span>{file.name}</span>
//                                                                 <DownloadOutlined
//                                                                     style={{ cursor: 'pointer', marginLeft: 10, color: '#1a73e8' }}
//                                                                     onClick={() => {
//                                                                         decodeBase64(file.content, file.name);
//                                                                     }}
//                                                                 />
//                                                             </li>
//                                                         ))}
//                                                     </ul>
//                                                 </div>
//                                             )
//                                         }
//                                     </Collapse.Panel>
//                                 ))}
//                             </Collapse> */}

//                             <Collapse defaultActiveKey={['0']} accordion>
//                                 {selectedEmail.replies?.map((reply, index) => (
//                                     <Collapse.Panel
//                                     key={index}
//                                     header={
//                                         <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
//                                         <span>{reply?.from}</span>
//                                         <span>{reply?.date}</span>
//                                         </div>
//                                     }
//                                     >
//                                     <div dangerouslySetInnerHTML={{ __html: cleanEmailBody(reply?.body) }} />
                                    
//                                     {reply.attachment?.length > 0 && (
//                                         <div style={{ marginTop: '15px' }}>
//                                         <strong>Attachments:</strong>
//                                         <ul style={{ listStyle: 'none', paddingLeft: 0 }}>
//                                             {reply.attachment.map((file, idx) => (
//                                             <li key={idx} style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
//                                                 <PaperClipOutlined style={{ marginRight: 8, color: "#1a73e8" }} />
//                                                 <span>{file.name}</span>
//                                                 <DownloadOutlined
//                                                 style={{ cursor: 'pointer', marginLeft: 10, color: '#1a73e8' }}
//                                                 onClick={() => decodeBase64(file.content, file.name)}
//                                                 />
//                                             </li>
//                                             ))}
//                                         </ul>
//                                         </div>
//                                     )}

//                                     {/* Render nested replies recursively */}
//                                     {reply.replies?.length > 0 && (
//                                         <div style={{ marginTop: 20, borderLeft: '2px solid #f0f0f0', paddingLeft: 16 }}>
//                                         <Collapse defaultActiveKey={['0']}>
//                                             {reply.replies.map((nestedReply, nestedIndex) => (
//                                             <Collapse.Panel
//                                                 key={nestedIndex}
//                                                 header={
//                                                 <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
//                                                     <span>{nestedReply?.from}</span>
//                                                     <span>{nestedReply?.date}</span>
//                                                 </div>
//                                                 }
//                                             >
//                                                 <div dangerouslySetInnerHTML={{ __html: cleanEmailBody(nestedReply?.body) }} />
                                                
//                                                 {nestedReply.attachment?.length > 0 && (
//                                                 <div style={{ marginTop: '15px' }}>
//                                                     <strong>Attachments:</strong>
//                                                     <ul style={{ listStyle: 'none', paddingLeft: 0 }}>
//                                                     {nestedReply.attachment.map((file, idx) => (
//                                                         <li key={idx} style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
//                                                         <PaperClipOutlined style={{ marginRight: 8, color: "#1a73e8" }} />
//                                                         <span>{file.name}</span>
//                                                         <DownloadOutlined
//                                                             style={{ cursor: 'pointer', marginLeft: 10, color: '#1a73e8' }}
//                                                             onClick={() => decodeBase64(file.content, file.name)}
//                                                         />
//                                                         </li>
//                                                     ))}
//                                                     </ul>
//                                                 </div>
//                                                 )}
//                                             </Collapse.Panel>
//                                             ))}
//                                         </Collapse>
//                                         </div>
//                                     )}
//                                     </Collapse.Panel>
//                                 ))}
//                                 </Collapse>

//                             <div style={{ marginTop: 24, paddingTop: 16, borderTop: "1px solid #f0f0f0" }}>
//                                 <Title level={4}>Reply</Title>
//                                 <TextArea
//                                     rows={5}
//                                     placeholder="Write your reply here..."
//                                     value={replyMessage}
//                                     onChange={(e) => setReplyMessage(e.target.value)}
//                                     style={{ marginBottom: 16 }}
//                                 />

//                                 <Upload
//                                     beforeUpload={() => false}
//                                     onChange={handleFileChange}
//                                     fileList={fileList}
//                                     multiple
//                                 >
//                                     <Button icon={<UploadOutlined />}>Attach Files</Button>
//                                 </Upload>

//                                 <div style={{ marginTop: 16 }}>
//                                     <Button type="primary" onClick={handleReplySend} loading={loading}>
//                                         Send Reply
//                                     </Button>
//                                 </div>
//                             </div>
//                         </div>
//                     ) : loading ? (
//                         <div style={styles.loadingContainer}>
//                             <Spin size="large" />
//                         </div>
//                     ) : emails.length === 0 ? (
//                         <p style={styles.error}>You have No Emails</p>
//                     ) : (
//                         <>
//                             <ul style={styles.emailList}>
//                                 {emails.map((email) => (
//                                     <li
//                                         key={email.id}
//                                         style={styles.emailItem}
//                                         onClick={() => setSelectedEmail(email)}
//                                     >
//                                         <div style={styles.emailItemHeader}>
//                                             <span style={styles.emailSubjectPreview}>{email.subject}</span>
//                                             <span style={styles.emailDatePreview}>{email.date}</span>
//                                         </div>
//                                         <div style={styles.emailSubHeader}>
//                                             <span style={styles.emailFrom}>From: {email.from}</span>
//                                         </div>
//                                         <div>
//                                             {email.body.split('\n')[0]} {/* Show only first line */}
//                                         </div>
//                                     </li>
//                                 ))}
//                             </ul>
//                             {!selectedEmail && !loading && emails.length > 0 && (
//                                 <div style={styles.pagination}>
//                                     <button
//                                         onClick={() => changePage(page - 1)}
//                                         disabled={page === 1}
//                                         style={styles.pageButton}
//                                     >
//                                         <LeftOutlined /> Previous
//                                     </button>
//                                     <span style={styles.pageInfo}>
//                                         Page {page} of {totalPages}
//                                     </span>
//                                     <button
//                                         onClick={() => changePage(page + 1)}
//                                         disabled={page >= totalPages}
//                                         style={styles.pageButton}
//                                     >
//                                         Next <RightOutlined />
//                                     </button>
//                                 </div>
//                             )}
//                         </>
//                     )}
//                 </div>


//             </div>
//         </div>
//     );
// }


import { useEffect, useState, useRef } from "react";
import {
    MailOutlined,
    InboxOutlined,
    DownloadOutlined,
    LeftOutlined,
    RightOutlined,
    PaperClipOutlined,
    UploadOutlined,
} from "@ant-design/icons";
import axios from "axios";
import { Button, Input, Upload, message, Typography, Spin, Collapse } from "antd";
import DOMPurify from "dompurify";
import { getEmails, replyEmail } from "../config/routes";

const apiClient = axios.create({
    baseURL: process.env.REACT_APP_baseURL,
});

function useWindowSize() {
    const [size, setSize] = useState({
        width: window.innerWidth,
        height: window.innerHeight,
    });
    useEffect(() => {
        const handleResize = () =>
            setSize({ width: window.innerWidth, height: window.innerHeight });
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);
    return size;
}

const ReplyItem = ({ reply, onReply, cleanEmailBody, decodeBase64 }) => {
    const [replyingTo, setReplyingTo] = useState(null);
    const [replyMessage, setReplyMessage] = useState("");
    const [fileList, setFileList] = useState([]);
    const [loading, setLoading] = useState(false);

    const handleReplySend = async () => {
        try {
            setLoading(true);
            const token = sessionStorage.getItem("agent_token");
            const formData = new FormData();
            
            // Build the proper references chain
            const existingRefs = reply.references ? 
                reply.references.split(/\s+/).filter(Boolean) : 
                [];
            
            // If the reply has its own in_reply_to, include that too
            if (reply.in_reply_to && !existingRefs.includes(reply.in_reply_to)) {
                existingRefs.unshift(reply.in_reply_to);
            }
            
            // Add the message we're directly replying to
            if (!existingRefs.includes(reply.message_id)) {
                existingRefs.push(reply.message_id);
            }
            
            // Remove duplicates while preserving order
            const references = [...new Set(existingRefs)].join(' ');

            formData.append("message_id", reply.message_id); // The immediate parent
            formData.append("email", reply.from);
            formData.append("subject", `Re: ${reply.subject.replace(/^Re:\s*/i, '')}`);
            formData.append("body", replyMessage);
            formData.append("in_reply_to", reply.message_id); // Direct parent
            formData.append("references", references); // Complete thread history

            // Handle attachments
            if (fileList.length > 0) {
                fileList.forEach(file => {
                    formData.append("attachments[]", file.originFileObj);
                });
            }

            const response = await apiClient.post(replyEmail, formData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "multipart/form-data",
                },
            });

            // Create new reply with proper threading headers
            const newReply = {
                subject: `Re: ${reply.subject.replace(/^Re:\s*/i, '')}`,
                from: reply.from,
                date: new Date().toISOString(),
                body: replyMessage,
                attachment: fileList.map(file => ({
                    name: file.name,
                    content: ""
                })),
                message_id: response.data.message_id || `temp-${Date.now()}@example.com`,
                in_reply_to: reply.message_id,
                references: references,
                replies: []
            };

            onReply(reply.message_id, newReply);
            message.success("Reply sent successfully!");
            setReplyMessage("");
            setFileList([]);
            setReplyingTo(null);
        } catch (error) {
            console.error("Reply send failed:", error);
            message.error("Failed to send reply. Please try again.");
        } finally {
            setLoading(false);
        }
    };
    const handleFileChange = ({ fileList }) => {
        setFileList(fileList);
    };

    return (
        <div>
            <div dangerouslySetInnerHTML={{ __html: cleanEmailBody(reply?.body) }} />
            
            {reply.attachment?.length > 0 && (
                <div style={{ marginTop: '15px' }}>
                    <strong>Attachments:</strong>
                    <ul style={{ listStyle: 'none', paddingLeft: 0 }}>
                        {reply.attachment.map((file, idx) => (
                            <li key={idx} style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
                                <PaperClipOutlined style={{ marginRight: 8, color: "#1a73e8" }} />
                                <span>{file.name}</span>
                                <DownloadOutlined
                                    style={{ cursor: 'pointer', marginLeft: 10, color: '#1a73e8' }}
                                    onClick={() => decodeBase64(file.content, file.name)}
                                />
                            </li>
                        ))}
                    </ul>
                </div>
            )}

            <div style={{ marginTop: 16 }}>
                {!replyingTo && (
                    <Button 
                        type="link" 
                        onClick={() => setReplyingTo(reply.message_id)}
                        style={{ paddingLeft: 0 }}
                    >
                        Reply
                    </Button>
                )}

                {replyingTo === reply.message_id && (
                    <div style={{ marginTop: 16, borderLeft: '2px solid #1a73e8', paddingLeft: 16 }}>
                        <Input.TextArea
                            rows={3}
                            placeholder="Write your reply here..."
                            value={replyMessage}
                            onChange={(e) => setReplyMessage(e.target.value)}
                            style={{ marginBottom: 8 }}
                        />
                        <Upload
                            beforeUpload={() => false}
                            onChange={handleFileChange}
                            fileList={fileList}
                            multiple
                        >
                            <Button icon={<UploadOutlined />}>Attach Files</Button>
                        </Upload>
                        <div style={{ marginTop: 8 }}>
                            <Button 
                                type="primary" 
                                onClick={handleReplySend} 
                                loading={loading}
                                style={{ marginRight: 8 }}
                            >
                                Send
                            </Button>
                            <Button onClick={() => setReplyingTo(null)}>
                                Cancel
                            </Button>
                        </div>
                    </div>
                )}
            </div>

            {reply.replies?.length > 0 && (
                <div style={{ marginTop: 20, borderLeft: '2px solid #f0f0f0', paddingLeft: 16 }}>
                    <Collapse defaultActiveKey={['0']}>
                        {reply.replies.map((nestedReply, index) => (
                            <Collapse.Panel
                                key={index}
                                header={
                                    <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                                        <span>{nestedReply?.from}</span>
                                        <span>{nestedReply?.date}</span>
                                    </div>
                                }
                            >
                                <ReplyItem 
                                    reply={nestedReply} 
                                    onReply={(parentId, newReply) => {
                                        // Find the parent reply and add the new reply
                                        if (reply.message_id === parentId) {
                                            reply.replies.push(newReply);
                                        }
                                    }}
                                    cleanEmailBody={cleanEmailBody}
                                    decodeBase64={decodeBase64}
                                />
                            </Collapse.Panel>
                        ))}
                    </Collapse>
                </div>
            )}
        </div>
    );
};

export function MyModal({ closeModal }) {
    const [emails, setEmails] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedEmail, setSelectedEmail] = useState(null);
    const [page, setPage] = useState(1);
    const [paginationData, setPaginationData] = useState({
        page: 1,
        per_page: 5,
        total: 0
    });
    const [sessionExpired, setSessionExpired] = useState(false);
    const [replyMessage, setReplyMessage] = useState("");
    const { width, height } = useWindowSize();
    const showSidebar = width >= 889 && height >= 889;
    const [fileList, setFileList] = useState([]);
    const [initialLoad, setInitialLoad] = useState(true);
    const token = sessionStorage.getItem("agent_token");

    useEffect(() => {
        if (initialLoad || page !== 1) {
            fetchEmails();
        }
    }, [page]);

    const fetchEmails = async () => {
        if (!token) {
            setSessionExpired(true);
            setLoading(false);
            return;
        }
        
        try {
            setLoading(true);
            const response = await apiClient.get(`${getEmails}${page}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    Accept: "application/json",
                },
            });
            
            if (response.data) {
                setEmails(response.data.emails || []);
                setPaginationData({
                    page: parseInt(response.data.page) || 1,
                    per_page: parseInt(response.data.per_page) || 5,
                    total: parseInt(response.data.total) || 0
                });
                setLoading(false);
            }
        } catch (error) {
            console.error("Error fetching emails:", error);
            if (error.response?.status === 401) {
                setSessionExpired(true);
            }
            setEmails([]);
        } finally {
            setLoading(false);
        }
    };

    const changePage = (newPage) => {
        const totalPages = Math.ceil(paginationData.total / paginationData.per_page);
        if (newPage < 1 || newPage > totalPages) return;
        setPage(newPage);
    };

    const handleReplySend = async () => {
        if (!replyMessage.trim()) {
            message.warning("Reply cannot be empty.");
            return;
        }

        if (!selectedEmail?.message_id) {
            message.error("No email selected to reply.");
            return;
        }

        try {
            setLoading(true);
            const token = sessionStorage.getItem("agent_token");
            const formData = new FormData();
            
            // Build the proper references chain
            const existingRefs = selectedEmail.references ? 
                selectedEmail.references.split(/\s+/).filter(Boolean) : 
                [];
            
            // If the email we're replying to has its own in_reply_to, include that too
            if (selectedEmail.in_reply_to && !existingRefs.includes(selectedEmail.in_reply_to)) {
                existingRefs.unshift(selectedEmail.in_reply_to);
            }
            
            // Add the message we're directly replying to
            if (!existingRefs.includes(selectedEmail.message_id)) {
                existingRefs.push(selectedEmail.message_id);
            }
            
            // Remove duplicates while preserving order
            const references = [...new Set(existingRefs)].join(' ');

            formData.append("message_id", selectedEmail.message_id); // The immediate parent
            formData.append("email", selectedEmail.from);
            formData.append("subject", `Re: ${selectedEmail.subject.replace(/^Re:\s*/i, '')}`);
            formData.append("body", replyMessage);
            formData.append("in_reply_to", selectedEmail.message_id); // Direct parent
            formData.append("references", references); // Complete thread history

            // Handle attachments
            fileList.forEach(file => {
                formData.append("attachments[]", file.originFileObj);
            });

            const response = await apiClient.post(replyEmail, formData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "multipart/form-data",
                },
            });

            // Create the new reply object with proper threading headers
            const newReply = {
                subject: `Re: ${selectedEmail.subject.replace(/^Re:\s*/i, '')}`,
                from: selectedEmail.from,
                date: new Date().toISOString(),
                body: replyMessage,
                attachment: fileList.map(file => ({
                    name: file.name,
                    content: file.originFileObj ? "" : file.content
                })),
                message_id: response.data.message_id || `temp-${Date.now()}@example.com`,
                in_reply_to: selectedEmail.message_id, // Direct parent
                references: references, // Complete thread
                replies: []
            };

            // Update the UI
            setSelectedEmail(prev => ({
                ...prev,
                replies: [...(prev.replies || []), newReply]
            }));

            message.success("Reply sent successfully!");
            setReplyMessage("");
            setFileList([]);
        } catch (error) {
            console.error("Reply send failed:", error);
            message.error("Failed to send reply. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleFileChange = ({ fileList }) => {
        setFileList(fileList);
    };

    const handleNestedReply = (parentId, newReply) => {
        const addReplyToEmail = (email) => {
            if (email.message_id === parentId) {
                return {
                    ...email,
                    replies: [...(email.replies || []), newReply]
                };
            }
            
            if (email.replies) {
                return {
                    ...email,
                    replies: email.replies.map(reply => addReplyToEmail(reply))
                };
            }
            
            return email;
        };

        setSelectedEmail(prev => addReplyToEmail(prev));
    };

    const decodeBase64 = (base64String, fileName) => {
        try {
            const link = document.createElement("a");
            link.href = `data:application/octet-stream;base64,${base64String}`;
            link.download = fileName || "attachment";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error("Error downloading file:", error);
            message.error("Failed to download attachment");
        }
    };

    function cleanEmailBody(rawHtml) {
        if (!rawHtml) return "";
        let cleaned = rawHtml
            .replace(/^>+ ?/gm, "")
            .replace(/\n{2,}/g, "\n\n")
            .trim();
        return DOMPurify.sanitize(cleaned);
    }

    const totalPages = Math.ceil(paginationData.total / paginationData.per_page);
    const { TextArea } = Input;
    const { Title } = Typography;

    return (
        <div style={styles.backdrop}>
            <div style={styles.modal}>
                {showSidebar && (
                    <div style={styles.sidebar}>
                        <h3 style={styles.sidebarTitle}>
                            <MailOutlined style={{ marginRight: 8 }} /> Mail
                        </h3>
                        <div
                            style={{
                                ...styles.menuItem,
                                backgroundColor: selectedEmail === null ? "#5f6368" : "#f1f3f4",
                                color: selectedEmail === null ? "#fff" : "#5f6368",
                            }}
                            onClick={() => setSelectedEmail(null)}
                        >
                            <InboxOutlined style={{ marginRight: 8 }} /> Inbox
                        </div>
                    </div>
                )}
                <div style={styles.content}>
                    <div style={styles.header}>
                        <h2 style={styles.headerTitle}>
                            {selectedEmail ? "Email Details" : "Inbox"}
                        </h2>
                        <button onClick={closeModal} style={styles.closeButton}>
                            Close
                        </button>
                    </div>
                    {selectedEmail && (
                        <button
                            style={styles.backButton}
                            onClick={() => setSelectedEmail(null)}
                        >
                            <LeftOutlined style={{ marginRight: 5 }} /> Back
                        </button>
                    )}
                    {sessionExpired ? (
                        <div style={styles.sessionExpired}>
                            <p>Session expired or login failed! Please try refreshing or logging in again.</p>
                        </div>
                    ) : selectedEmail ? (
                        <div style={styles.emailDetails}>
                            <h3 style={styles.emailSubject}>{selectedEmail.subject}</h3>
                            <p style={styles.emailFromDetail}>
                                <strong>From:</strong> {selectedEmail.from}
                            </p>
                            <p style={styles.emailDate}>
                                <strong>Date:</strong> {selectedEmail.date}
                            </p>
                            <div style={styles.emailBody}>
                                <strong>Body:</strong> 
                                <div dangerouslySetInnerHTML={{ __html: cleanEmailBody(selectedEmail.body) }} />
                            </div>
                            
                            {selectedEmail.attachment?.length > 0 && (
                                <div style={{ marginBottom: 15 }}>
                                    <h4 style={{ marginBottom: 10, fontWeight: 700, marginTop: 15 }}>Attachments</h4>
                                    {selectedEmail.attachment.map((file, index) => (
                                        <div key={index} style={styles.attachment}>
                                            <PaperClipOutlined style={{ marginRight: 8, color: "#1a73e8" }} />
                                            <span>{file.name}</span>
                                            <DownloadOutlined
                                                style={{ cursor: "pointer", marginLeft: 10, color: "#1a73e8" }}
                                                onClick={() => decodeBase64(file.content, file.name)}
                                            />
                                        </div>
                                    ))}
                                </div>
                            )}

                            <Collapse defaultActiveKey={['0']} accordion>
                                {selectedEmail.replies?.map((reply, index) => (
                                    <Collapse.Panel
                                        key={index}
                                        header={
                                            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                                                <span>{reply?.from}</span>
                                                <span>{reply?.date}</span>
                                            </div>
                                        }
                                    >
  
                                    <ReplyItem 
                                        reply={reply} 
                                        onReply={handleNestedReply}
                                        cleanEmailBody={cleanEmailBody}
                                        decodeBase64={decodeBase64}
                                    />
                                    </Collapse.Panel>
                                ))}
                            </Collapse>

                            <div style={{ marginTop: 24, paddingTop: 16, borderTop: "1px solid #f0f0f0" }}>
                                <Title level={4}>Reply</Title>
                                <TextArea
                                    rows={5}
                                    placeholder="Write your reply here..."
                                    value={replyMessage}
                                    onChange={(e) => setReplyMessage(e.target.value)}
                                    style={{ marginBottom: 16 }}
                                />

                                <Upload
                                    beforeUpload={() => false}
                                    onChange={handleFileChange}
                                    fileList={fileList}
                                    multiple
                                >
                                    <Button icon={<UploadOutlined />}>Attach Files</Button>
                                </Upload>

                                <div style={{ marginTop: 16 }}>
                                    <Button 
                                        type="primary" 
                                        onClick={handleReplySend} 
                                        loading={loading}
                                    >
                                        Send Reply
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ) : loading ? (
                        <div style={styles.loadingContainer}>
                            <Spin size="large" />
                        </div>
                    ) : emails.length === 0 ? (
                        <p style={styles.error}>You have No Emails</p>
                    ) : (
                        <>
                            <ul style={styles.emailList}>
                                {emails.map((email) => (
                                    <li
                                        key={email.message_id}
                                        style={styles.emailItem}
                                        onClick={() => setSelectedEmail(email)}
                                    >
                                        <div style={styles.emailItemHeader}>
                                            <span style={styles.emailSubjectPreview}>{email.subject}</span>
                                            <span style={styles.emailDatePreview}>{email.date}</span>
                                        </div>
                                        <div style={styles.emailSubHeader}>
                                            <span style={styles.emailFrom}>From: {email.from}</span>
                                        </div>
                                        <div style={styles.emailBodyPreview}>
                                            {email.body?.split('\n')[0]}
                                        </div>
                                    </li>
                                ))}
                            </ul>
                            {!selectedEmail && !loading && emails.length > 0 && (
                                <div style={styles.pagination}>
                                    <button
                                        onClick={() => changePage(page - 1)}
                                        disabled={page === 1}
                                        style={styles.pageButton}
                                    >
                                        <LeftOutlined /> Previous
                                    </button>
                                    <span style={styles.pageInfo}>
                                        Page {page} of {totalPages}
                                    </span>
                                    <button
                                        onClick={() => changePage(page + 1)}
                                        disabled={page >= totalPages}
                                        style={styles.pageButton}
                                    >
                                        Next <RightOutlined />
                                    </button>
                                </div>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}

const styles = {
    backdrop: {
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.1)",
        zIndex: 9999,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontFamily: "'Roboto', sans-serif",
    },
    modal: {
        backgroundColor: "#fff",
        width: "80%",
        height: "80%",
        minWidth: "375px",
        // minHeight: "667px",
        maxWidth: "1500px",
        maxHeight: "950px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        display: "flex",
        borderRadius: "8px",
        overflow: "hidden",
        flexDirection: "row",
        transition: "flex-direction 0.3s ease",
    },
    sidebar: {
        width: "200px",
        backgroundColor: "#f1f3f4",
        padding: "24px 16px",
        display: "flex",
        flexDirection: "column",
        borderRight: "1px solid #e0e0e0",
    },
    sidebarTitle: { fontSize: "22px", marginBottom: "20px", fontWeight: "bold", color: "#5f6368" },
    menuItem: {
        padding: "12px 16px",
        cursor: "pointer",
        fontWeight: "500",
        borderRadius: "4px",
        marginBottom: "8px",
        backgroundColor: "#f1f3f4",
        color: "#5f6368",
    },
    content: { flex: 1, padding: "20px 30px", backgroundColor: "#fff", overflowY: "auto" },
    header: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: "20px",
        borderBottom: "1px solid #f1f3f4",
        paddingBottom: "10px",
    },
    headerTitle: { fontSize: "26px", fontWeight: "500", color: "#202124" },
    closeButton: {
        padding: "6px 16px",
        cursor: "pointer",
        backgroundColor: "#d93025",
        color: "#fff",
        border: "1px solid #e0e0e0",
        borderRadius: "50px",
        fontSize: "14px",
        transition: "background-color 0.2s ease, color 0.2s ease",
        height: "40px",
    },
    backButton: {
        padding: "6px 12px",
        cursor: "pointer",
        height: "40px",
        backgroundColor: "#1a73e8",
        color: "#fff",
        border: "1px solid #e0e0e0",
        borderRadius: "50px",
        fontSize: "14px",
        display: "inline-flex",
        alignItems: "center",
        marginBottom: "15px",
        transition: "background-color 0.2s ease, color 0.2s ease",
    },
    sessionExpired: { textAlign: "center", marginTop: "50px", fontSize: "16px", color: "#d93025" },
    loadingContainer: { display: "flex", justifyContent: "center", alignItems: "center", height: "60vh" },
    error: { fontSize: "18px", textAlign: "center", color: "#d93025", fontWeight: "bold" },
    emailList: { display: "grid", gridTemplateColumns: "1fr", gap: "10px", listStyle: "none", padding: 0, margin: 0 },
    emailItem: {
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        padding: "15px 20px",
        marginBottom: "5px",
        border: "1px solid #e0e0e0",
        borderRadius: "8px",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        backgroundColor: "#fff",
        cursor: "pointer",
        transition: "background-color 0.2s, transform 0.2s",
        height: "66px",
    },
    emailItemHeader: { display: "flex", justifyContent: "space-between", alignItems: "center", gap: "10px" },
    emailSubjectPreview: { fontSize: "17px", fontWeight: "600", color: "#202124" },
    emailDatePreview: { fontSize: "14px", color: "#5f6368" },
    emailSubHeader: { margin: "8px", marginLeft: "0px" },
    emailFrom: { fontSize: "14px", color: "#5f6368" },
    emailDetails: {
        padding: "20px",
        backgroundColor: "#f8f9fa",
        border: "1px solid #e0e0e0",
        borderRadius: "6px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
    },
    // emailSubject: { fontSize: "20px", fontWeight: "700", marginBottom: "25px", color: "#202124" },
    emailSubject: {
        fontSize: "20px",
        fontWeight: "700",
        marginBottom: "25px",
        color: "#202124",
        maxHeight: "400px",
        overflowY: "auto",
        scroll: "hidden",
    },
    emailFromDetail: { fontSize: "16px", color: "#5f6368", marginBottom: "25px" },
    emailDate: { fontSize: "15px", color: "#5f6368", marginTop: "5px", marginBottom: "20px" },
    emailBody: {
        fontSize: "16px",
        color: "#202124",
        lineHeight: "1.6",
        marginTop: "20px",
        marginBottom: "20px",
        maxHeight: "400px",
        overflowY: "auto",
    },
    emailBodyPreview: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        fontSize: '14px',
        color: '#5f6368',
    },
    pagination: { display: "flex", justifyContent: "space-between", alignItems: "center", marginTop: "20px" },
    pageButton: {
        padding: "8px 15px",
        cursor: "pointer",
        borderRadius: "4px",
        border: "1px solid #1a73e8",
        backgroundColor: "#1a73e8",
        fontSize: "14px",
        color: "#fff",
    },
    pageInfo: { fontSize: "16px", color: "#202124" },

    // Media queries
    "@media (max-width: 899px)": {
        modal: { width: "90%", height: "90%", flexDirection: "column" },
        sidebar: { borderRight: "none" },
        content: { padding: "15px 20px" },
        headerTitle: { fontSize: "22px" },
        emailSubjectPreview: { fontSize: "16px" },
        emailDatePreview: { fontSize: "12px" },
        emailSubject: { fontSize: "22px" },
        emailBody: { fontSize: "14px" },
        pagination: { flexDirection: "column", alignItems: "center" },
        pageButton: { width: "100%", padding: "10px" },
    },

    "@media (max-width: 767px)": {
        modal: { width: "100%", height: "100%", flexDirection: "column" },
        content: { padding: "12px 16px" },
        headerTitle: { fontSize: "20px" },
        emailSubjectPreview: { fontSize: "14px" },
        emailDatePreview: { fontSize: "10px" },
        emailSubject: { fontSize: "18px" },
        emailBody: { fontSize: "12px" },
        pagination: { flexDirection: "column", alignItems: "center" },
        pageButton: { width: "100%", padding: "8px" },
    },

    "@media (max-width: 889px) and (max-height: 889px)": {
        modal: { width: "100%", height: "100%", flexDirection: "column" },
    },

    "@media (max-width: 650px) and (max-height: 889px)": {
        emailItem: { height: "auto", padding: "15px 20px", overflow: "visible" },
    },

    replySection: {
        marginTop: 20,
        paddingTop: 10,
    },
    replyHeading: {
        marginBottom: 20,
        fontWeight: "bold",
    },
    replyInput: {
        width: "100%",
        height: 100,
        padding: 10,
        fontSize: 14,
        marginBottom: 10,
        borderRadius: 4,
        border: "1px solid #ccc",
    },
    sendReplyButton: {
        backgroundColor: "#1a73e8",
        color: "#fff",
        border: "none",
        padding: "10px 16px",
        borderRadius: 4,
        cursor: "pointer",
    },

};

export default MyModal;
