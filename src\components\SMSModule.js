import { Form, Input, Modal, Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { SaveOutlined } from "@ant-design/icons";
import { getSMSByCategory, getSMSCategory, sendSMS } from "../Actions/SMSAction";
import openSuccessNotificationWithIcon from "./Message";

export const SMSModule = ({ smsVisible, setSmsVisible }) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [number, setNumber] = useState("")
    const [smsType, setSmsType] = useState('')


    const smsState = useSelector(state => state.SMSReducer)
    
    useEffect(() => dispatch(getSMSCategory()), [])

    useEffect(() => {
        if (smsState.message == "OK") {
            if (number)
                openSuccessNotificationWithIcon('success', `Message has been send to ${number}`)
        }
        else if (smsState.message) openSuccessNotificationWithIcon('error', smsState.message)
    }, [smsState.message])

    useEffect(() => {
        if (!smsVisible) {
            form.resetFields();
            setSmsType("");
        }
    }, [smsVisible]);
    

    return (
        <Modal
            visible={smsVisible}
            onCancel={() => {
                setSmsVisible(false)
                setSmsType("")
                form.resetFields()
            }}
            okText="Submit"
            title="Send Sms"
            onOk={() =>
                form
                .validateFields()
                .then((values) => {
                    dispatch(sendSMS(values))
                    setNumber(values.number)
                    setSmsVisible(false)
                    setSmsType("")
                    form.resetFields()
                })
                .catch((e) => console.log(e))
            }
            okButtonProps={{
                loading: smsState.isLoading,
                icon: <SaveOutlined />,
            }}
        >

            <Form form={form} labelCol={{ span: 24 }}>
                <Form.Item name={'number'} label="Number" rules={[
                    {
                        required: true,
                        message: 'Please input your number!',
                    },
                ]}>
                    <Input placeholder="Enter number!" />
                </Form.Item>
                <Form.Item name="category_id" label="SMS Category" rules={[
                    {
                        required: true,
                        message: 'Please insert your sms category!',
                    },
                ]}>
                    <Select onChange={(v) => {
                        setSmsType(v)
                        dispatch(getSMSByCategory(v))
                    }} placeholder="Select a option and change SMS template">
                        <Select.Option value="custom_sms">Custom SMS</Select.Option>
                        {smsState && smsState.category?.map(v => <Select.Option key={v.id} value={v.id}>{v.name}</Select.Option>)}
                    </Select>
                </Form.Item>

                {smsType && <Form.Item name="sms" label="SMS" rules={[
                    {
                        required: true,
                        message: 'Please select sms!',
                    },
                ]}>
                    {smsType === "custom_sms" ? <Input.TextArea rows={5} /> : <Select placeholder="Select a SMS">
                        {smsState.sms && smsState.sms?.s_m_s_template?.map(v => <Select.Option value={v.id}>{v.msg}</Select.Option>)}
                    </Select>}
                </Form.Item>}
            </Form>

        </Modal>
    );
};