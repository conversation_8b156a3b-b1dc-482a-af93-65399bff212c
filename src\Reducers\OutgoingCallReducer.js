import * as ActionTypes from "../Constants/OutgoingCallConstant"

const initialState = {
    outgoingCallId: "",
    errMess: "",
    isLoading: false
}

export const OutgoingCallReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.OUTGOING_ID_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.OUTGOING_ID_SUCCESS:
            return { ...state, isLoading: false, outgoingCallId: action.payload }
        case "RESET_CALLID":
            return { ...state, isLoading: false, outgoingCallId: "" }
        case ActionTypes.OUTGOING_ID_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
    }
}